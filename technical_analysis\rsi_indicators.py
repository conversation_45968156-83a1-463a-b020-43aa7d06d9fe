"""
📊 مؤشرات القوة النسبية (RSI)
تحتوي على RSI5, RSI14 مع تحليل مناطق التشبع والإشارات
"""

from typing import Dict, List, Any
import pandas as pd
import numpy as np
from datetime import datetime
import logging

from .base_indicator import BaseIndicator
from config.constants import TechnicalIndicators, INDICATOR_THRESHOLDS

logger = logging.getLogger(__name__)


class RSIIndicator(BaseIndicator):
    """
    مؤشر القوة النسبية (Relative Strength Index)
    يحسب RSI ويحلل مناطق التشبع والإشارات
    """
    
    def __init__(self, period: int = 14, **kwargs):
        """
        تهيئة مؤشر RSI
        
        Args:
            period: فترة RSI (5 أو 14)
            **kwargs: معاملات إضافية
        """
        # تحديد نوع المؤشر حسب الفترة
        if period == 5:
            indicator_type = TechnicalIndicators.RSI5
        elif period == 14:
            indicator_type = TechnicalIndicators.RSI14
        else:
            # للفترات المخصصة
            indicator_type = TechnicalIndicators.RSI14
        
        super().__init__(indicator_type, period=period, **kwargs)
        self.period = period
        
        # عتبات RSI
        self.oversold_threshold = INDICATOR_THRESHOLDS.get('rsi_oversold', 30)
        self.overbought_threshold = INDICATOR_THRESHOLDS.get('rsi_overbought', 70)
        self.neutral_low = INDICATOR_THRESHOLDS.get('rsi_neutral_low', 40)
        self.neutral_high = INDICATOR_THRESHOLDS.get('rsi_neutral_high', 60)
        
        logger.info(f"تم تهيئة مؤشر RSI{period}")
    
    def calculate(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        حساب قيم RSI
        
        Args:
            data: بيانات الشموع مع أعمدة OHLC
            
        Returns:
            قاموس يحتوي على قيم RSI المحسوبة
        """
        try:
            if not self.validate_data(data):
                raise ValueError("البيانات غير صحيحة")
            
            close_prices = data['close'].astype(float)
            
            # حساب التغيرات السعرية
            price_changes = close_prices.diff()
            
            # فصل المكاسب والخسائر
            gains = price_changes.where(price_changes > 0, 0)
            losses = -price_changes.where(price_changes < 0, 0)
            
            # حساب متوسط المكاسب والخسائر
            avg_gains = gains.rolling(window=self.period).mean()
            avg_losses = losses.rolling(window=self.period).mean()
            
            # حساب RS (Relative Strength)
            rs = avg_gains / avg_losses
            rs = rs.replace([np.inf, -np.inf], 100)  # معالجة القسمة على صفر
            
            # حساب RSI
            rsi = 100 - (100 / (1 + rs))
            
            # القيم الحالية والسابقة
            current_rsi = float(rsi.iloc[-1])
            previous_rsi = float(rsi.iloc[-2]) if len(rsi) > 1 else current_rsi
            
            # حساب التغيير في RSI
            rsi_change = current_rsi - previous_rsi
            rsi_momentum = rsi_change
            
            # تحديد المنطقة
            if current_rsi <= self.oversold_threshold:
                zone = "تشبع بيعي"
                zone_strength = (self.oversold_threshold - current_rsi) / self.oversold_threshold
            elif current_rsi >= self.overbought_threshold:
                zone = "تشبع شرائي"
                zone_strength = (current_rsi - self.overbought_threshold) / (100 - self.overbought_threshold)
            elif self.neutral_low <= current_rsi <= self.neutral_high:
                zone = "منطقة محايدة"
                zone_strength = 0.0
            elif current_rsi < self.neutral_low:
                zone = "ميل هبوطي"
                zone_strength = (self.neutral_low - current_rsi) / (self.neutral_low - self.oversold_threshold)
            else:  # current_rsi > self.neutral_high
                zone = "ميل صعودي"
                zone_strength = (current_rsi - self.neutral_high) / (self.overbought_threshold - self.neutral_high)
            
            zone_strength = min(max(zone_strength, 0.0), 1.0)
            
            # تحليل الاتجاه
            if rsi_change > 1:
                trend = "صاعد"
                trend_strength = min(abs(rsi_change) / 10, 1.0)
            elif rsi_change < -1:
                trend = "هابط"
                trend_strength = min(abs(rsi_change) / 10, 1.0)
            else:
                trend = "جانبي"
                trend_strength = 0.0
            
            # حساب التباعد (إذا توفرت بيانات كافية)
            divergence = self._detect_divergence(close_prices, rsi)
            
            # حساب قوة الإشارة
            signal_strength = self._calculate_signal_strength(current_rsi, rsi_change, zone_strength)
            
            result = {
                'rsi_value': current_rsi,
                'previous_rsi': previous_rsi,
                'rsi_change': rsi_change,
                'rsi_momentum': rsi_momentum,
                'zone': zone,
                'zone_strength': zone_strength,
                'trend': trend,
                'trend_strength': trend_strength,
                'signal_strength': signal_strength,
                'divergence': divergence,
                'avg_gains': float(avg_gains.iloc[-1]),
                'avg_losses': float(avg_losses.iloc[-1]),
                'rs': float(rs.iloc[-1]) if not np.isinf(rs.iloc[-1]) else 100,
                'period': self.period,
                'thresholds': {
                    'oversold': self.oversold_threshold,
                    'overbought': self.overbought_threshold,
                    'neutral_low': self.neutral_low,
                    'neutral_high': self.neutral_high
                },
                'calculation_time': datetime.now()
            }
            
            # تحديث التخزين المؤقت
            self.update_cache('last_calculation', result)
            
            return result
            
        except Exception as e:
            logger.error(f"خطأ في حساب RSI{self.period}: {str(e)}")
            raise
    
    def _detect_divergence(self, prices: pd.Series, rsi: pd.Series) -> Dict[str, Any]:
        """
        كشف التباعد بين السعر و RSI
        
        Args:
            prices: أسعار الإغلاق
            rsi: قيم RSI
            
        Returns:
            معلومات التباعد
        """
        try:
            if len(prices) < 10 or len(rsi) < 10:
                return {'type': 'لا يوجد', 'strength': 0.0}
            
            # أخذ آخر 10 نقاط للتحليل
            recent_prices = prices.tail(10)
            recent_rsi = rsi.tail(10)
            
            # حساب الاتجاه العام للسعر و RSI
            price_trend = recent_prices.iloc[-1] - recent_prices.iloc[0]
            rsi_trend = recent_rsi.iloc[-1] - recent_rsi.iloc[0]
            
            # كشف التباعد
            if price_trend > 0 and rsi_trend < 0:
                return {'type': 'تباعد هبوطي', 'strength': min(abs(rsi_trend) / 20, 1.0)}
            elif price_trend < 0 and rsi_trend > 0:
                return {'type': 'تباعد صعودي', 'strength': min(abs(rsi_trend) / 20, 1.0)}
            else:
                return {'type': 'لا يوجد', 'strength': 0.0}
                
        except Exception:
            return {'type': 'خطأ في الحساب', 'strength': 0.0}
    
    def _calculate_signal_strength(self, rsi_value: float, rsi_change: float, zone_strength: float) -> float:
        """
        حساب قوة الإشارة
        
        Args:
            rsi_value: قيمة RSI الحالية
            rsi_change: التغيير في RSI
            zone_strength: قوة المنطقة
            
        Returns:
            قوة الإشارة (0-1)
        """
        strength = 0.0
        
        # قوة المنطقة
        strength += zone_strength * 0.4
        
        # قوة التغيير
        if abs(rsi_change) > 2:
            strength += min(abs(rsi_change) / 10, 0.3)
        
        # مناطق التشبع
        if rsi_value <= self.oversold_threshold or rsi_value >= self.overbought_threshold:
            strength += 0.3
        
        return min(strength, 1.0)
    
    def get_signal(self, current_value: Dict[str, Any], previous_values: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        تحليل إشارة RSI
        
        Args:
            current_value: القيمة الحالية لـ RSI
            previous_values: القيم السابقة (للتحليل المتقدم)
            
        Returns:
            تحليل الإشارة
        """
        try:
            # استخراج البيانات
            rsi_value = current_value.get('rsi_value', 50)
            rsi_change = current_value.get('rsi_change', 0)
            zone = current_value.get('zone', 'منطقة محايدة')
            zone_strength = current_value.get('zone_strength', 0)
            divergence = current_value.get('divergence', {})
            signal_strength = current_value.get('signal_strength', 0)
            
            # تحليل الإشارة الأساسية
            direction = "NEUTRAL"
            base_strength = 0.1
            
            # إشارات التشبع
            if rsi_value <= self.oversold_threshold and rsi_change > 0:
                direction = "CALL"  # خروج من التشبع البيعي
                base_strength = 0.8
            elif rsi_value >= self.overbought_threshold and rsi_change < 0:
                direction = "PUT"  # خروج من التشبع الشرائي
                base_strength = 0.8
            # إشارات الاتجاه
            elif rsi_value < self.neutral_low and rsi_change > 2:
                direction = "CALL"  # بداية اتجاه صعودي
                base_strength = 0.6
            elif rsi_value > self.neutral_high and rsi_change < -2:
                direction = "PUT"  # بداية اتجاه هبوطي
                base_strength = 0.6
            # إشارات ضعيفة
            elif self.neutral_low < rsi_value < self.neutral_high:
                direction = "NEUTRAL"  # منطقة محايدة
                base_strength = 0.2
            
            # تعديل القوة حسب التباعد
            strength_multiplier = 1.0
            if divergence.get('type') == 'تباعد صعودي' and direction == "CALL":
                strength_multiplier += divergence.get('strength', 0) * 0.5
            elif divergence.get('type') == 'تباعد هبوطي' and direction == "PUT":
                strength_multiplier += divergence.get('strength', 0) * 0.5
            
            # تعديل القوة حسب قوة المنطقة
            strength_multiplier += zone_strength * 0.3
            
            # حساب القوة النهائية
            final_strength = min(base_strength * strength_multiplier, 1.0)
            
            # حساب مستوى الثقة
            confidence = final_strength
            
            # زيادة الثقة في مناطق التشبع
            if rsi_value <= self.oversold_threshold or rsi_value >= self.overbought_threshold:
                confidence *= 1.2
            
            # زيادة الثقة مع التباعد
            if divergence.get('strength', 0) > 0.5:
                confidence *= 1.1
            
            confidence = min(confidence, 1.0)
            
            # تحديد الوزن حسب فترة RSI
            if self.period == 5:
                weight = 0.9  # RSI قصير المدى - وزن أقل
            elif self.period == 14:
                weight = 1.1  # RSI متوسط المدى - وزن أكبر
            else:
                weight = 1.0
            
            signal = {
                'direction': direction,
                'strength': final_strength,
                'confidence': confidence,
                'weight': weight,
                'details': {
                    'rsi_value': rsi_value,
                    'zone': zone,
                    'zone_strength': zone_strength,
                    'rsi_change': rsi_change,
                    'divergence': divergence,
                    'thresholds': current_value.get('thresholds', {})
                },
                'reasoning': self._get_signal_reasoning(direction, current_value),
                'timestamp': datetime.now()
            }
            
            return signal
            
        except Exception as e:
            logger.error(f"خطأ في تحليل إشارة RSI{self.period}: {str(e)}")
            return {
                'direction': 'NEUTRAL',
                'strength': 0.0,
                'confidence': 0.0,
                'weight': 1.0,
                'error': str(e)
            }
    
    def _get_signal_reasoning(self, direction: str, current_value: Dict[str, Any]) -> str:
        """
        الحصول على تفسير الإشارة
        
        Args:
            direction: اتجاه الإشارة
            current_value: القيمة الحالية
            
        Returns:
            تفسير نصي للإشارة
        """
        rsi_value = current_value.get('rsi_value', 50)
        zone = current_value.get('zone', 'غير محدد')
        divergence = current_value.get('divergence', {}).get('type', 'لا يوجد')
        
        if direction == "CALL":
            return f"RSI{self.period}={rsi_value:.1f} في {zone} مع {divergence} - إشارة شراء"
        elif direction == "PUT":
            return f"RSI{self.period}={rsi_value:.1f} في {zone} مع {divergence} - إشارة بيع"
        else:
            return f"RSI{self.period}={rsi_value:.1f} في {zone} - إشارة محايدة"
    
    def get_minimum_periods(self) -> int:
        """الحد الأدنى من الفترات المطلوبة"""
        return max(self.period * 2, 30)  # ضعف الفترة على الأقل


# كلاسات محددة لكل فترة RSI
class RSI5(RSIIndicator):
    """مؤشر RSI لفترة 5"""
    def __init__(self, **kwargs):
        super().__init__(period=5, **kwargs)


class RSI14(RSIIndicator):
    """مؤشر RSI لفترة 14"""
    def __init__(self, **kwargs):
        super().__init__(period=14, **kwargs)
