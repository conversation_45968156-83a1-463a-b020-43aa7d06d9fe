"""
📊 مؤشرات متوسط المدى الحقيقي (ATR)
تحتوي على ATR5, ATR14 مع تحليل التقلبات والإشارات
"""

from typing import Dict, List, Any
import pandas as pd
import numpy as np
from datetime import datetime
import logging

from .base_indicator import BaseIndicator
from config.constants import TechnicalIndicators

logger = logging.getLogger(__name__)


class ATRIndicator(BaseIndicator):
    """
    مؤشر متوسط المدى الحقيقي (Average True Range)
    يقيس التقلبات ويحلل قوة الحركة السعرية
    """
    
    def __init__(self, period: int = 14, **kwargs):
        """
        تهيئة مؤشر ATR
        
        Args:
            period: فترة ATR (5 أو 14)
            **kwargs: معاملات إضافية
        """
        # تحديد نوع المؤشر حسب الفترة
        if period == 5:
            indicator_type = TechnicalIndicators.ATR5
        elif period == 14:
            indicator_type = TechnicalIndicators.ATR14
        else:
            # للفترات المخصصة
            indicator_type = TechnicalIndicators.ATR14
        
        super().__init__(indicator_type, period=period, **kwargs)
        self.period = period
        
        logger.info(f"تم تهيئة مؤشر ATR{period}")
    
    def calculate(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        حساب قيم ATR
        
        Args:
            data: بيانات الشموع مع أعمدة OHLC
            
        Returns:
            قاموس يحتوي على قيم ATR المحسوبة
        """
        try:
            if not self.validate_data(data):
                raise ValueError("البيانات غير صحيحة")
            
            high_prices = data['high'].astype(float)
            low_prices = data['low'].astype(float)
            close_prices = data['close'].astype(float)
            
            # حساب True Range
            # TR = max(High - Low, |High - Previous Close|, |Low - Previous Close|)
            previous_close = close_prices.shift(1)
            
            tr1 = high_prices - low_prices  # High - Low
            tr2 = abs(high_prices - previous_close)  # |High - Previous Close|
            tr3 = abs(low_prices - previous_close)   # |Low - Previous Close|
            
            true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            
            # حساب ATR (متوسط متحرك للـ True Range)
            atr = true_range.rolling(window=self.period).mean()
            
            # حساب ATR النسبي (كنسبة مئوية من السعر)
            atr_percentage = (atr / close_prices) * 100
            
            # القيم الحالية والسابقة
            current_atr = float(atr.iloc[-1]) if not pd.isna(atr.iloc[-1]) else 0.0
            previous_atr = float(atr.iloc[-2]) if len(atr) > 1 and not pd.isna(atr.iloc[-2]) else current_atr
            
            current_atr_pct = float(atr_percentage.iloc[-1]) if not pd.isna(atr_percentage.iloc[-1]) else 0.0
            previous_atr_pct = float(atr_percentage.iloc[-2]) if len(atr_percentage) > 1 and not pd.isna(atr_percentage.iloc[-2]) else current_atr_pct
            
            current_tr = float(true_range.iloc[-1]) if not pd.isna(true_range.iloc[-1]) else 0.0
            current_price = float(close_prices.iloc[-1])
            
            # حساب التغيير في ATR
            atr_change = current_atr - previous_atr
            atr_change_pct = ((current_atr / previous_atr) - 1) * 100 if previous_atr != 0 else 0.0
            
            # تحليل مستوى التقلبات
            volatility_analysis = self._analyze_volatility_level(current_atr_pct, atr_percentage)
            
            # تحليل اتجاه التقلبات
            volatility_trend = self._analyze_volatility_trend(atr, atr_percentage)
            
            # تحليل قوة الحركة الحالية
            movement_analysis = self._analyze_current_movement(current_tr, current_atr, current_price)
            
            # حساب مستويات الدعم والمقاومة المتوقعة
            support_resistance = self._calculate_atr_levels(current_price, current_atr)
            
            # تحليل إشارات التداول
            trading_signals = self._analyze_trading_signals(current_atr_pct, atr_change_pct, volatility_analysis)
            
            result = {
                'atr_value': current_atr,
                'atr_percentage': current_atr_pct,
                'previous_atr': previous_atr,
                'previous_atr_pct': previous_atr_pct,
                'atr_change': atr_change,
                'atr_change_percentage': atr_change_pct,
                'true_range': current_tr,
                'true_range_percentage': (current_tr / current_price) * 100 if current_price != 0 else 0.0,
                'volatility_analysis': volatility_analysis,
                'volatility_trend': volatility_trend,
                'movement_analysis': movement_analysis,
                'support_resistance': support_resistance,
                'trading_signals': trading_signals,
                'current_price': current_price,
                'period': self.period,
                'calculation_time': datetime.now()
            }
            
            # تحديث التخزين المؤقت
            self.update_cache('last_calculation', result)
            
            return result
            
        except Exception as e:
            logger.error(f"خطأ في حساب ATR{self.period}: {str(e)}")
            raise
    
    def _analyze_volatility_level(self, current_atr_pct: float, atr_pct_series: pd.Series) -> Dict[str, Any]:
        """
        تحليل مستوى التقلبات
        
        Args:
            current_atr_pct: ATR النسبي الحالي
            atr_pct_series: سلسلة ATR النسبي
            
        Returns:
            تحليل مستوى التقلبات
        """
        try:
            if len(atr_pct_series) < 20:
                return {'level': 'غير محدد', 'percentile': 50, 'description': 'بيانات غير كافية'}
            
            # حساب المئينات للفترات السابقة
            recent_atr = atr_pct_series.tail(50).dropna()
            if len(recent_atr) < 10:
                return {'level': 'غير محدد', 'percentile': 50, 'description': 'بيانات غير كافية'}
            
            percentile = (recent_atr < current_atr_pct).sum() / len(recent_atr) * 100
            
            # تصنيف مستوى التقلبات
            if percentile >= 90:
                level = 'عالي جداً'
                description = 'تقلبات استثنائية - حذر شديد'
            elif percentile >= 75:
                level = 'عالي'
                description = 'تقلبات عالية - فرص وأخطار'
            elif percentile >= 60:
                level = 'فوق المتوسط'
                description = 'تقلبات متزايدة'
            elif percentile >= 40:
                level = 'متوسط'
                description = 'تقلبات طبيعية'
            elif percentile >= 25:
                level = 'منخفض'
                description = 'تقلبات محدودة'
            else:
                level = 'منخفض جداً'
                description = 'سوق هادئ - انتظار حركة'
            
            return {
                'level': level,
                'percentile': percentile,
                'description': description,
                'current_atr_pct': current_atr_pct,
                'avg_atr_pct': float(recent_atr.mean())
            }
            
        except Exception:
            return {'level': 'خطأ', 'percentile': 50, 'description': 'خطأ في الحساب'}
    
    def _analyze_volatility_trend(self, atr_series: pd.Series, atr_pct_series: pd.Series) -> Dict[str, Any]:
        """
        تحليل اتجاه التقلبات
        
        Args:
            atr_series: سلسلة ATR
            atr_pct_series: سلسلة ATR النسبي
            
        Returns:
            تحليل اتجاه التقلبات
        """
        try:
            if len(atr_series) < 10:
                return {'direction': 'غير محدد', 'strength': 0.0, 'duration': 0}
            
            recent_atr = atr_series.tail(10).dropna()
            if len(recent_atr) < 5:
                return {'direction': 'غير محدد', 'strength': 0.0, 'duration': 0}
            
            # حساب الاتجاه العام
            slope = (recent_atr.iloc[-1] - recent_atr.iloc[0]) / len(recent_atr)
            slope_percentage = (slope / recent_atr.iloc[0]) * 100 if recent_atr.iloc[0] != 0 else 0
            
            # تحديد الاتجاه
            if slope_percentage > 2:
                direction = 'متزايد'
                strength = min(abs(slope_percentage) / 10, 1.0)
            elif slope_percentage < -2:
                direction = 'متناقص'
                strength = min(abs(slope_percentage) / 10, 1.0)
            else:
                direction = 'مستقر'
                strength = 0.0
            
            # حساب مدة الاتجاه
            duration = 1
            if len(recent_atr) > 1:
                for i in range(1, len(recent_atr)):
                    if direction == 'متزايد' and recent_atr.iloc[-i] < recent_atr.iloc[-(i+1)]:
                        duration += 1
                    elif direction == 'متناقص' and recent_atr.iloc[-i] > recent_atr.iloc[-(i+1)]:
                        duration += 1
                    else:
                        break
            
            return {
                'direction': direction,
                'strength': strength,
                'duration': duration,
                'slope_percentage': slope_percentage
            }
            
        except Exception:
            return {'direction': 'خطأ', 'strength': 0.0, 'duration': 0}
    
    def _analyze_current_movement(self, true_range: float, atr: float, price: float) -> Dict[str, Any]:
        """
        تحليل قوة الحركة الحالية
        
        Args:
            true_range: المدى الحقيقي الحالي
            atr: متوسط المدى الحقيقي
            price: السعر الحالي
            
        Returns:
            تحليل الحركة الحالية
        """
        try:
            # نسبة الحركة الحالية إلى ATR
            movement_ratio = true_range / atr if atr != 0 else 0
            movement_percentage = (true_range / price) * 100 if price != 0 else 0
            
            # تصنيف قوة الحركة
            if movement_ratio > 2.0:
                strength = 'قوية جداً'
                score = 1.0
            elif movement_ratio > 1.5:
                strength = 'قوية'
                score = 0.8
            elif movement_ratio > 1.0:
                strength = 'متوسطة'
                score = 0.6
            elif movement_ratio > 0.5:
                strength = 'ضعيفة'
                score = 0.4
            else:
                strength = 'ضعيفة جداً'
                score = 0.2
            
            return {
                'strength': strength,
                'score': score,
                'movement_ratio': movement_ratio,
                'movement_percentage': movement_percentage,
                'true_range': true_range,
                'atr': atr
            }
            
        except Exception:
            return {'strength': 'خطأ', 'score': 0.0, 'movement_ratio': 0.0}
    
    def _calculate_atr_levels(self, price: float, atr: float) -> Dict[str, Any]:
        """
        حساب مستويات الدعم والمقاومة بناء على ATR
        
        Args:
            price: السعر الحالي
            atr: قيمة ATR
            
        Returns:
            مستويات الدعم والمقاومة
        """
        try:
            # مستويات متعددة بناء على ATR
            levels = {
                'resistance_1': price + atr,
                'resistance_2': price + (atr * 1.5),
                'resistance_3': price + (atr * 2.0),
                'support_1': price - atr,
                'support_2': price - (atr * 1.5),
                'support_3': price - (atr * 2.0),
                'stop_loss_long': price - (atr * 1.5),
                'stop_loss_short': price + (atr * 1.5),
                'take_profit_long': price + (atr * 2.0),
                'take_profit_short': price - (atr * 2.0)
            }
            
            return levels
            
        except Exception:
            return {
                'resistance_1': price,
                'support_1': price,
                'stop_loss_long': price,
                'stop_loss_short': price
            }
    
    def _analyze_trading_signals(self, atr_pct: float, atr_change_pct: float, volatility_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        تحليل إشارات التداول بناء على ATR
        
        Args:
            atr_pct: ATR النسبي
            atr_change_pct: تغيير ATR النسبي
            volatility_analysis: تحليل التقلبات
            
        Returns:
            إشارات التداول
        """
        try:
            signals = []
            signal_strength = 0.0
            
            volatility_level = volatility_analysis.get('level', 'متوسط')
            percentile = volatility_analysis.get('percentile', 50)
            
            # إشارات بناء على مستوى التقلبات
            if percentile < 25:  # تقلبات منخفضة جداً
                signals.append('انتظار اختراق - تقلبات منخفضة')
                signal_strength += 0.3
            elif percentile > 90:  # تقلبات عالية جداً
                signals.append('حذر - تقلبات عالية')
                signal_strength += 0.2
            
            # إشارات بناء على تغيير ATR
            if atr_change_pct > 20:  # زيادة كبيرة في التقلبات
                signals.append('زيادة التقلبات - فرصة حركة قوية')
                signal_strength += 0.4
            elif atr_change_pct < -20:  # انخفاض كبير في التقلبات
                signals.append('انخفاض التقلبات - توقع استقرار')
                signal_strength += 0.3
            
            # تحديد ملاءمة السوق للتداول
            if 25 <= percentile <= 75:
                market_condition = 'مناسب للتداول'
                trading_suitability = 0.8
            elif percentile < 25:
                market_condition = 'سوق هادئ - انتظار'
                trading_suitability = 0.4
            else:
                market_condition = 'سوق متقلب - حذر'
                trading_suitability = 0.6
            
            return {
                'signals': signals,
                'signal_strength': min(signal_strength, 1.0),
                'market_condition': market_condition,
                'trading_suitability': trading_suitability,
                'volatility_level': volatility_level
            }
            
        except Exception:
            return {
                'signals': [],
                'signal_strength': 0.0,
                'market_condition': 'غير محدد',
                'trading_suitability': 0.5
            }
    
    def get_signal(self, current_value: Dict[str, Any], previous_values: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        تحليل إشارة ATR
        
        Args:
            current_value: القيمة الحالية لـ ATR
            previous_values: القيم السابقة (للتحليل المتقدم)
            
        Returns:
            تحليل الإشارة
        """
        try:
            # استخراج البيانات
            atr_pct = current_value.get('atr_percentage', 0)
            atr_change_pct = current_value.get('atr_change_percentage', 0)
            volatility_analysis = current_value.get('volatility_analysis', {})
            volatility_trend = current_value.get('volatility_trend', {})
            movement_analysis = current_value.get('movement_analysis', {})
            trading_signals = current_value.get('trading_signals', {})
            
            # ATR لا يعطي إشارات اتجاه مباشرة، بل يحدد قوة الحركة وملاءمة التداول
            direction = "NEUTRAL"  # ATR محايد بطبيعته
            
            # حساب قوة الإشارة بناء على ملاءمة السوق للتداول
            trading_suitability = trading_signals.get('trading_suitability', 0.5)
            signal_strength = trading_signals.get('signal_strength', 0.0)
            
            # تحديد قوة الإشارة النهائية
            base_strength = trading_suitability * 0.6 + signal_strength * 0.4
            
            # تعديل القوة حسب اتجاه التقلبات
            volatility_direction = volatility_trend.get('direction', 'مستقر')
            if volatility_direction == 'متزايد':
                base_strength += 0.2  # زيادة التقلبات تعني فرص أكبر
            elif volatility_direction == 'متناقص':
                base_strength -= 0.1  # انخفاض التقلبات يقلل الفرص
            
            final_strength = min(max(base_strength, 0.0), 1.0)
            
            # حساب مستوى الثقة
            confidence = final_strength
            
            # زيادة الثقة مع التقلبات المتوسطة (الأفضل للتداول)
            volatility_percentile = volatility_analysis.get('percentile', 50)
            if 30 <= volatility_percentile <= 70:
                confidence *= 1.1
            
            confidence = min(confidence, 1.0)
            
            # تحديد الوزن حسب فترة ATR
            if self.period == 5:
                weight = 0.7  # ATR قصير المدى - وزن أقل
            elif self.period == 14:
                weight = 0.8  # ATR متوسط المدى - وزن متوسط
            else:
                weight = 0.75
            
            signal = {
                'direction': direction,
                'strength': final_strength,
                'confidence': confidence,
                'weight': weight,
                'details': {
                    'atr_percentage': atr_pct,
                    'volatility_level': volatility_analysis.get('level', 'غير محدد'),
                    'volatility_percentile': volatility_percentile,
                    'market_condition': trading_signals.get('market_condition', 'غير محدد'),
                    'trading_suitability': trading_suitability,
                    'movement_strength': movement_analysis.get('strength', 'غير محدد'),
                    'volatility_trend': volatility_direction,
                    'support_resistance': current_value.get('support_resistance', {})
                },
                'reasoning': self._get_signal_reasoning(direction, current_value),
                'timestamp': datetime.now()
            }
            
            return signal
            
        except Exception as e:
            logger.error(f"خطأ في تحليل إشارة ATR{self.period}: {str(e)}")
            return {
                'direction': 'NEUTRAL',
                'strength': 0.0,
                'confidence': 0.0,
                'weight': 1.0,
                'error': str(e)
            }
    
    def _get_signal_reasoning(self, direction: str, current_value: Dict[str, Any]) -> str:
        """
        الحصول على تفسير الإشارة
        
        Args:
            direction: اتجاه الإشارة
            current_value: القيمة الحالية
            
        Returns:
            تفسير نصي للإشارة
        """
        atr_pct = current_value.get('atr_percentage', 0)
        volatility_level = current_value.get('volatility_analysis', {}).get('level', 'غير محدد')
        market_condition = current_value.get('trading_signals', {}).get('market_condition', 'غير محدد')
        
        return f"ATR{self.period}: {atr_pct:.2f}% تقلبات {volatility_level} - {market_condition}"
    
    def get_minimum_periods(self) -> int:
        """الحد الأدنى من الفترات المطلوبة"""
        return max(self.period * 2, 30)  # ضعف الفترة على الأقل


# كلاسات محددة لكل فترة ATR
class ATR5(ATRIndicator):
    """مؤشر ATR لفترة 5"""
    def __init__(self, **kwargs):
        super().__init__(period=5, **kwargs)


class ATR14(ATRIndicator):
    """مؤشر ATR لفترة 14"""
    def __init__(self, **kwargs):
        super().__init__(period=14, **kwargs)
