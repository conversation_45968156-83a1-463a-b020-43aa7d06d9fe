"""
🔧 محرك المؤشرات الفنية
يدير جميع المؤشرات الفنية ويوفر واجهة موحدة لحسابها وتحليلها
"""

from typing import Dict, List, Optional, Any, Type
import pandas as pd
import numpy as np
from datetime import datetime
import logging
import asyncio
from concurrent.futures import ThreadPoolExecutor

from .base_indicator import BaseIndicator, IndicatorResult
from config.constants import TechnicalIndicators, INDICATOR_SETTINGS

logger = logging.getLogger(__name__)


class IndicatorsEngine:
    """
    محرك المؤشرات الفنية الرئيسي
    يدير جميع المؤشرات ويوفر حساب متوازي وتحليل شامل
    """
    
    def __init__(self, enabled_indicators: Optional[List[str]] = None):
        """
        تهيئة محرك المؤشرات
        
        Args:
            enabled_indicators: قائمة المؤشرات المفعلة (None لتفعيل الكل)
        """
        self.indicators: Dict[str, BaseIndicator] = {}
        self.enabled_indicators = enabled_indicators or list(INDICATOR_SETTINGS.keys())
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # إحصائيات الأداء
        self.calculation_times = {}
        self.error_counts = {}
        
        logger.info(f"تم تهيئة محرك المؤشرات مع {len(self.enabled_indicators)} مؤشر")
    
    def register_indicator(self, indicator: BaseIndicator) -> None:
        """
        تسجيل مؤشر جديد في المحرك
        
        Args:
            indicator: المؤشر المراد تسجيله
        """
        if indicator.name in self.enabled_indicators:
            self.indicators[indicator.name] = indicator
            logger.info(f"تم تسجيل المؤشر: {indicator.name}")
        else:
            logger.warning(f"المؤشر {indicator.name} غير مفعل")
    
    def get_indicator(self, indicator_name: str) -> Optional[BaseIndicator]:
        """
        الحصول على مؤشر محدد
        
        Args:
            indicator_name: اسم المؤشر
            
        Returns:
            المؤشر أو None إذا لم يوجد
        """
        return self.indicators.get(indicator_name)
    
    def calculate_single_indicator(self, indicator_name: str, data: pd.DataFrame) -> Optional[IndicatorResult]:
        """
        حساب مؤشر واحد
        
        Args:
            indicator_name: اسم المؤشر
            data: بيانات الشموع
            
        Returns:
            نتيجة المؤشر أو None في حالة الخطأ
        """
        try:
            start_time = datetime.now()
            
            indicator = self.indicators.get(indicator_name)
            if not indicator:
                logger.error(f"المؤشر {indicator_name} غير موجود")
                return None
            
            # التحقق من صحة البيانات
            if not indicator.validate_data(data):
                logger.error(f"البيانات غير صحيحة للمؤشر {indicator_name}")
                return None
            
            # حساب المؤشر
            values = indicator.calculate(data)
            
            # تحليل الإشارة (نحتاج قيم سابقة للتحليل الكامل)
            signal = indicator.get_signal(values, [])
            
            # تسجيل وقت الحساب
            calculation_time = (datetime.now() - start_time).total_seconds()
            self.calculation_times[indicator_name] = calculation_time
            
            result = IndicatorResult(
                indicator_name=indicator_name,
                values=values,
                signal=signal,
                timestamp=datetime.now()
            )
            
            logger.debug(f"تم حساب المؤشر {indicator_name} في {calculation_time:.3f} ثانية")
            return result
            
        except Exception as e:
            self.error_counts[indicator_name] = self.error_counts.get(indicator_name, 0) + 1
            logger.error(f"خطأ في حساب المؤشر {indicator_name}: {str(e)}")
            return None
    
    def calculate_all_indicators(self, data: pd.DataFrame) -> Dict[str, IndicatorResult]:
        """
        حساب جميع المؤشرات المفعلة
        
        Args:
            data: بيانات الشموع
            
        Returns:
            قاموس يحتوي على نتائج جميع المؤشرات
        """
        results = {}
        
        try:
            logger.info(f"بدء حساب {len(self.indicators)} مؤشر")
            
            for indicator_name in self.indicators.keys():
                result = self.calculate_single_indicator(indicator_name, data)
                if result:
                    results[indicator_name] = result
            
            logger.info(f"تم حساب {len(results)} مؤشر بنجاح من أصل {len(self.indicators)}")
            
        except Exception as e:
            logger.error(f"خطأ في حساب المؤشرات: {str(e)}")
        
        return results
    
    async def calculate_all_indicators_async(self, data: pd.DataFrame) -> Dict[str, IndicatorResult]:
        """
        حساب جميع المؤشرات بشكل غير متزامن
        
        Args:
            data: بيانات الشموع
            
        Returns:
            قاموس يحتوي على نتائج جميع المؤشرات
        """
        try:
            logger.info(f"بدء الحساب غير المتزامن لـ {len(self.indicators)} مؤشر")
            
            # إنشاء مهام للحساب المتوازي
            tasks = []
            for indicator_name in self.indicators.keys():
                task = asyncio.get_event_loop().run_in_executor(
                    self.executor,
                    self.calculate_single_indicator,
                    indicator_name,
                    data
                )
                tasks.append((indicator_name, task))
            
            # انتظار اكتمال جميع المهام
            results = {}
            for indicator_name, task in tasks:
                try:
                    result = await task
                    if result:
                        results[indicator_name] = result
                except Exception as e:
                    logger.error(f"خطأ في الحساب غير المتزامن للمؤشر {indicator_name}: {str(e)}")
            
            logger.info(f"تم الحساب غير المتزامن لـ {len(results)} مؤشر بنجاح")
            return results
            
        except Exception as e:
            logger.error(f"خطأ في الحساب غير المتزامن للمؤشرات: {str(e)}")
            return {}
    
    def get_combined_signal(self, indicator_results: Dict[str, IndicatorResult]) -> Dict[str, Any]:
        """
        دمج إشارات جميع المؤشرات للحصول على إشارة موحدة
        
        Args:
            indicator_results: نتائج المؤشرات
            
        Returns:
            الإشارة الموحدة
        """
        try:
            signals = {name: result.signal for name, result in indicator_results.items()}
            
            # حساب الأوزان والنقاط
            total_weight = 0
            weighted_score = 0
            signal_count = {'CALL': 0, 'PUT': 0, 'NEUTRAL': 0}
            
            for indicator_name, signal in signals.items():
                direction = signal.get('direction', 'NEUTRAL')
                strength = signal.get('strength', 0.5)
                weight = signal.get('weight', 1.0)
                
                signal_count[direction] += 1
                
                if direction == 'CALL':
                    weighted_score += strength * weight
                elif direction == 'PUT':
                    weighted_score -= strength * weight
                
                total_weight += weight
            
            # تحديد الاتجاه النهائي
            if total_weight > 0:
                final_score = weighted_score / total_weight
            else:
                final_score = 0
            
            if final_score > 0.3:
                final_direction = 'CALL'
            elif final_score < -0.3:
                final_direction = 'PUT'
            else:
                final_direction = 'NEUTRAL'
            
            # حساب قوة الإشارة
            signal_strength = min(abs(final_score), 1.0)
            
            combined_signal = {
                'direction': final_direction,
                'strength': signal_strength,
                'confidence': signal_strength,
                'score': final_score,
                'signal_count': signal_count,
                'total_indicators': len(signals),
                'timestamp': datetime.now()
            }
            
            logger.debug(f"الإشارة الموحدة: {final_direction} بقوة {signal_strength:.2f}")
            return combined_signal
            
        except Exception as e:
            logger.error(f"خطأ في دمج الإشارات: {str(e)}")
            return {
                'direction': 'NEUTRAL',
                'strength': 0.0,
                'confidence': 0.0,
                'error': str(e)
            }
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        الحصول على إحصائيات الأداء
        
        Returns:
            إحصائيات شاملة عن أداء المحرك
        """
        return {
            'registered_indicators': len(self.indicators),
            'enabled_indicators': len(self.enabled_indicators),
            'calculation_times': self.calculation_times.copy(),
            'error_counts': self.error_counts.copy(),
            'average_calculation_time': np.mean(list(self.calculation_times.values())) if self.calculation_times else 0
        }
    
    def reset_stats(self) -> None:
        """إعادة تعيين الإحصائيات"""
        self.calculation_times.clear()
        self.error_counts.clear()
        logger.info("تم إعادة تعيين إحصائيات الأداء")
    
    def __del__(self):
        """تنظيف الموارد عند الحذف"""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=True)
