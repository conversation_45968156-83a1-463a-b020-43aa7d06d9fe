"""
📊 مؤشر المتوسط المتحرك البسيط (SMA)
يحسب SMA10 مع تحليل الإشارات والاتجاهات
"""

from typing import Dict, List, Any
import pandas as pd
import numpy as np
from datetime import datetime
import logging

from .base_indicator import BaseIndicator
from config.constants import TechnicalIndicators

logger = logging.getLogger(__name__)


class SMAIndicator(BaseIndicator):
    """
    مؤشر المتوسط المتحرك البسيط (Simple Moving Average)
    يحسب SMA ويحلل الإشارات والاتجاهات
    """
    
    def __init__(self, period: int = 10, **kwargs):
        """
        تهيئة مؤشر SMA
        
        Args:
            period: فترة المتوسط المتحرك (افتراضي 10)
            **kwargs: معاملات إضافية
        """
        super().__init__(TechnicalIndicators.SMA10, period=period, **kwargs)
        self.period = period
        
        logger.info(f"تم تهيئة مؤشر SMA{period}")
    
    def calculate(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        حساب قيم SMA
        
        Args:
            data: بيانات الشموع مع أعمدة OHLC
            
        Returns:
            قاموس يحتوي على قيم SMA المحسوبة
        """
        try:
            if not self.validate_data(data):
                raise ValueError("البيانات غير صحيحة")
            
            # حساب SMA للأسعار المختلفة
            close_prices = data['close'].astype(float)
            high_prices = data['high'].astype(float)
            low_prices = data['low'].astype(float)
            open_prices = data['open'].astype(float)
            
            # حساب SMA الأساسي (للإغلاق)
            sma_close = close_prices.rolling(window=self.period).mean()
            
            # حساب SMA للأسعار الأخرى للتحليل المتقدم
            sma_high = high_prices.rolling(window=self.period).mean()
            sma_low = low_prices.rolling(window=self.period).mean()
            sma_open = open_prices.rolling(window=self.period).mean()
            
            # حساب متوسط النطاق (High-Low)
            range_values = high_prices - low_prices
            sma_range = range_values.rolling(window=self.period).mean()
            
            # القيم الحالية والسابقة
            current_sma = float(sma_close.iloc[-1])
            previous_sma = float(sma_close.iloc[-2]) if len(sma_close) > 1 else current_sma
            
            # حساب الميل والاتجاه
            slope = current_sma - previous_sma
            slope_percentage = (slope / previous_sma) * 100 if previous_sma != 0 else 0
            
            # حساب المسافة من السعر الحالي
            current_price = float(close_prices.iloc[-1])
            distance_from_price = current_price - current_sma
            distance_percentage = (distance_from_price / current_sma) * 100 if current_sma != 0 else 0
            
            # حساب التقلبات حول SMA
            recent_prices = close_prices.tail(self.period)
            deviations = recent_prices - current_sma
            volatility = float(deviations.std())
            volatility_percentage = (volatility / current_sma) * 100 if current_sma != 0 else 0
            
            # تحديد الاتجاه وقوته
            if slope > 0:
                trend = "صاعد"
                trend_strength = min(abs(slope_percentage) * 10, 1.0)
            elif slope < 0:
                trend = "هابط"
                trend_strength = min(abs(slope_percentage) * 10, 1.0)
            else:
                trend = "جانبي"
                trend_strength = 0.0
            
            # تحليل موقع السعر بالنسبة لـ SMA
            if abs(distance_percentage) < 0.5:
                price_position = "قريب"
            elif distance_percentage > 0.5:
                price_position = "أعلى"
            else:
                price_position = "أقل"
            
            # حساب قوة الإشارة بناء على عدة عوامل
            signal_strength = 0.0
            if abs(slope_percentage) > 0.1:  # اتجاه واضح
                signal_strength += 0.3
            if abs(distance_percentage) > 1.0:  # مسافة كافية من SMA
                signal_strength += 0.4
            if volatility_percentage < 2.0:  # تقلبات منخفضة (استقرار)
                signal_strength += 0.3
            
            signal_strength = min(signal_strength, 1.0)
            
            result = {
                'sma_value': current_sma,
                'previous_sma': previous_sma,
                'slope': slope,
                'slope_percentage': slope_percentage,
                'trend': trend,
                'trend_strength': trend_strength,
                'distance_from_price': distance_from_price,
                'distance_percentage': distance_percentage,
                'current_price': current_price,
                'price_position': price_position,
                'volatility': volatility,
                'volatility_percentage': volatility_percentage,
                'signal_strength': signal_strength,
                'sma_high': float(sma_high.iloc[-1]),
                'sma_low': float(sma_low.iloc[-1]),
                'sma_open': float(sma_open.iloc[-1]),
                'sma_range': float(sma_range.iloc[-1]),
                'period': self.period,
                'calculation_time': datetime.now()
            }
            
            # تحديث التخزين المؤقت
            self.update_cache('last_calculation', result)
            
            return result
            
        except Exception as e:
            logger.error(f"خطأ في حساب SMA{self.period}: {str(e)}")
            raise
    
    def get_signal(self, current_value: Dict[str, Any], previous_values: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        تحليل إشارة SMA
        
        Args:
            current_value: القيمة الحالية لـ SMA
            previous_values: القيم السابقة (للتحليل المتقدم)
            
        Returns:
            تحليل الإشارة
        """
        try:
            # استخراج البيانات
            current_price = current_value.get('current_price', 0)
            sma_value = current_value.get('sma_value', 0)
            slope = current_value.get('slope', 0)
            trend_strength = current_value.get('trend_strength', 0)
            distance_percentage = current_value.get('distance_percentage', 0)
            volatility_percentage = current_value.get('volatility_percentage', 0)
            signal_strength = current_value.get('signal_strength', 0)
            
            # تحليل الإشارة الأساسية
            direction = "NEUTRAL"
            base_strength = 0.1
            
            # إشارة صعود
            if current_price > sma_value and slope > 0:
                direction = "CALL"
                base_strength = 0.7
            # إشارة هبوط
            elif current_price < sma_value and slope < 0:
                direction = "PUT"
                base_strength = 0.7
            # إشارات ضعيفة أو متضاربة
            elif current_price > sma_value and slope <= 0:
                direction = "NEUTRAL"  # السعر أعلى لكن الاتجاه هابط
                base_strength = 0.3
            elif current_price < sma_value and slope >= 0:
                direction = "NEUTRAL"  # السعر أقل لكن الاتجاه صاعد
                base_strength = 0.3
            
            # تعديل القوة حسب عوامل إضافية
            strength_multiplier = 1.0
            
            # عامل المسافة من SMA
            if abs(distance_percentage) > 1.0:
                strength_multiplier += 0.3  # مسافة جيدة تزيد القوة
            elif abs(distance_percentage) < 0.2:
                strength_multiplier -= 0.2  # مسافة قليلة تقلل القوة
            
            # عامل التقلبات
            if volatility_percentage < 1.5:
                strength_multiplier += 0.2  # تقلبات منخفضة = استقرار
            elif volatility_percentage > 3.0:
                strength_multiplier -= 0.3  # تقلبات عالية = عدم استقرار
            
            # عامل قوة الاتجاه
            strength_multiplier += trend_strength * 0.5
            
            # حساب القوة النهائية
            final_strength = min(base_strength * strength_multiplier, 1.0)
            final_strength = max(final_strength, 0.0)
            
            # حساب مستوى الثقة
            confidence = final_strength
            
            # زيادة الثقة في حالات معينة
            if abs(distance_percentage) > 1.5 and trend_strength > 0.5:
                confidence *= 1.2
            if volatility_percentage < 1.0:  # استقرار عالي
                confidence *= 1.1
            
            confidence = min(confidence, 1.0)
            
            # تحديد الوزن (SMA له وزن متوسط)
            weight = 1.0
            
            signal = {
                'direction': direction,
                'strength': final_strength,
                'confidence': confidence,
                'weight': weight,
                'details': {
                    'price_vs_sma': 'أعلى' if current_price > sma_value else 'أقل',
                    'trend': current_value.get('trend', 'غير محدد'),
                    'slope': slope,
                    'distance_percentage': distance_percentage,
                    'volatility_percentage': volatility_percentage,
                    'trend_strength': trend_strength,
                    'price_position': current_value.get('price_position', 'غير محدد')
                },
                'reasoning': self._get_signal_reasoning(direction, current_value),
                'timestamp': datetime.now()
            }
            
            return signal
            
        except Exception as e:
            logger.error(f"خطأ في تحليل إشارة SMA{self.period}: {str(e)}")
            return {
                'direction': 'NEUTRAL',
                'strength': 0.0,
                'confidence': 0.0,
                'weight': 1.0,
                'error': str(e)
            }
    
    def _get_signal_reasoning(self, direction: str, current_value: Dict[str, Any]) -> str:
        """
        الحصول على تفسير الإشارة
        
        Args:
            direction: اتجاه الإشارة
            current_value: القيمة الحالية
            
        Returns:
            تفسير نصي للإشارة
        """
        price_vs_sma = 'أعلى' if current_value.get('current_price', 0) > current_value.get('sma_value', 0) else 'أقل'
        trend = current_value.get('trend', 'غير محدد')
        volatility = current_value.get('volatility_percentage', 0)
        
        if direction == "CALL":
            return f"السعر {price_vs_sma} من SMA{self.period} والاتجاه {trend} مع تقلبات {volatility:.1f}% - إشارة شراء"
        elif direction == "PUT":
            return f"السعر {price_vs_sma} من SMA{self.period} والاتجاه {trend} مع تقلبات {volatility:.1f}% - إشارة بيع"
        else:
            return f"إشارة محايدة - السعر {price_vs_sma} من SMA{self.period} والاتجاه {trend}"
    
    def get_minimum_periods(self) -> int:
        """الحد الأدنى من الفترات المطلوبة"""
        return max(self.period * 2, 20)  # ضعف الفترة على الأقل


# كلاس محدد لـ SMA10
class SMA10(SMAIndicator):
    """مؤشر SMA لفترة 10"""
    def __init__(self, **kwargs):
        super().__init__(period=10, **kwargs)
