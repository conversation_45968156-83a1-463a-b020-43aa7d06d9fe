"""
📊 مؤشر النتيجة المعيارية (Z-Score)
يحسب Z-Score لتحديد مدى انحراف السعر عن المتوسط الإحصائي
"""

from typing import Dict, List, Any
import pandas as pd
import numpy as np
from datetime import datetime
import logging

from .base_indicator import BaseIndicator
from config.constants import TechnicalIndicators, INDICATOR_THRESHOLDS

logger = logging.getLogger(__name__)


class ZScoreIndicator(BaseIndicator):
    """
    مؤشر النتيجة المعيارية (Z-Score)
    يقيس مدى انحراف السعر الحالي عن المتوسط الإحصائي
    """
    
    def __init__(self, period: int = 20, **kwargs):
        """
        تهيئة مؤشر Z-Score
        
        Args:
            period: فترة حساب المتوسط والانحراف المعياري (افتراضي 20)
            **kwargs: معاملات إضافية
        """
        super().__init__(TechnicalIndicators.Z_SCORE, period=period, **kwargs)
        self.period = period
        
        # عتبات Z-Score
        self.extreme_threshold = INDICATOR_THRESHOLDS.get('z_score_extreme', 2.0)
        self.moderate_threshold = INDICATOR_THRESHOLDS.get('z_score_moderate', 1.5)
        
        logger.info(f"تم تهيئة مؤشر Z-Score({period})")
    
    def calculate(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        حساب قيم Z-Score
        
        Args:
            data: بيانات الشموع مع أعمدة OHLC
            
        Returns:
            قاموس يحتوي على قيم Z-Score المحسوبة
        """
        try:
            if not self.validate_data(data):
                raise ValueError("البيانات غير صحيحة")
            
            close_prices = data['close'].astype(float)
            high_prices = data['high'].astype(float)
            low_prices = data['low'].astype(float)
            
            # حساب المتوسط المتحرك والانحراف المعياري
            rolling_mean = close_prices.rolling(window=self.period).mean()
            rolling_std = close_prices.rolling(window=self.period).std()
            
            # حساب Z-Score للإغلاق
            z_score_close = (close_prices - rolling_mean) / rolling_std
            
            # حساب Z-Score للأسعار الأخرى
            z_score_high = (high_prices - rolling_mean) / rolling_std
            z_score_low = (low_prices - rolling_mean) / rolling_std
            
            # حساب Z-Score للمدى (High - Low)
            price_range = high_prices - low_prices
            range_mean = price_range.rolling(window=self.period).mean()
            range_std = price_range.rolling(window=self.period).std()
            z_score_range = (price_range - range_mean) / range_std
            
            # القيم الحالية والسابقة
            current_z_score = float(z_score_close.iloc[-1]) if not pd.isna(z_score_close.iloc[-1]) else 0.0
            previous_z_score = float(z_score_close.iloc[-2]) if len(z_score_close) > 1 and not pd.isna(z_score_close.iloc[-2]) else current_z_score
            
            current_price = float(close_prices.iloc[-1])
            current_mean = float(rolling_mean.iloc[-1]) if not pd.isna(rolling_mean.iloc[-1]) else current_price
            current_std = float(rolling_std.iloc[-1]) if not pd.isna(rolling_std.iloc[-1]) else 0.0
            
            # حساب التغيير في Z-Score
            z_score_change = current_z_score - previous_z_score
            
            # تحليل مستوى الانحراف
            deviation_analysis = self._analyze_deviation_level(current_z_score)
            
            # تحليل الاتجاه
            trend_analysis = self._analyze_z_score_trend(z_score_close)
            
            # كشف حالات التشبع
            saturation_analysis = self._analyze_saturation(current_z_score, z_score_change)
            
            # تحليل الانعكاس المحتمل
            reversal_analysis = self._analyze_reversal_probability(current_z_score, previous_z_score, z_score_close)
            
            # حساب مستويات الدعم والمقاومة الإحصائية
            statistical_levels = self._calculate_statistical_levels(current_mean, current_std)
            
            # تحليل التقلبات
            volatility_analysis = self._analyze_volatility(current_std, rolling_std)
            
            result = {
                'z_score': current_z_score,
                'previous_z_score': previous_z_score,
                'z_score_change': z_score_change,
                'z_score_high': float(z_score_high.iloc[-1]) if not pd.isna(z_score_high.iloc[-1]) else 0.0,
                'z_score_low': float(z_score_low.iloc[-1]) if not pd.isna(z_score_low.iloc[-1]) else 0.0,
                'z_score_range': float(z_score_range.iloc[-1]) if not pd.isna(z_score_range.iloc[-1]) else 0.0,
                'current_price': current_price,
                'rolling_mean': current_mean,
                'rolling_std': current_std,
                'price_deviation': current_price - current_mean,
                'price_deviation_pct': ((current_price - current_mean) / current_mean) * 100 if current_mean != 0 else 0.0,
                'deviation_analysis': deviation_analysis,
                'trend_analysis': trend_analysis,
                'saturation_analysis': saturation_analysis,
                'reversal_analysis': reversal_analysis,
                'statistical_levels': statistical_levels,
                'volatility_analysis': volatility_analysis,
                'period': self.period,
                'thresholds': {
                    'extreme': self.extreme_threshold,
                    'moderate': self.moderate_threshold
                },
                'calculation_time': datetime.now()
            }
            
            # تحديث التخزين المؤقت
            self.update_cache('last_calculation', result)
            
            return result
            
        except Exception as e:
            logger.error(f"خطأ في حساب Z-Score: {str(e)}")
            raise
    
    def _analyze_deviation_level(self, z_score: float) -> Dict[str, Any]:
        """
        تحليل مستوى الانحراف
        
        Args:
            z_score: قيمة Z-Score
            
        Returns:
            تحليل مستوى الانحراف
        """
        try:
            abs_z_score = abs(z_score)
            
            # تصنيف مستوى الانحراف
            if abs_z_score >= self.extreme_threshold:
                level = 'انحراف شديد'
                significance = 'عالي جداً'
                probability = 0.95  # احتمالية عودة للمتوسط
            elif abs_z_score >= self.moderate_threshold:
                level = 'انحراف متوسط'
                significance = 'عالي'
                probability = 0.80
            elif abs_z_score >= 1.0:
                level = 'انحراف خفيف'
                significance = 'متوسط'
                probability = 0.65
            elif abs_z_score >= 0.5:
                level = 'قريب من المتوسط'
                significance = 'منخفض'
                probability = 0.50
            else:
                level = 'عند المتوسط'
                significance = 'منخفض جداً'
                probability = 0.40
            
            # تحديد الاتجاه
            if z_score > 0:
                direction = 'أعلى من المتوسط'
                position = 'مرتفع'
            elif z_score < 0:
                direction = 'أقل من المتوسط'
                position = 'منخفض'
            else:
                direction = 'عند المتوسط'
                position = 'متوسط'
            
            return {
                'level': level,
                'significance': significance,
                'direction': direction,
                'position': position,
                'probability_mean_reversion': probability,
                'abs_z_score': abs_z_score,
                'percentile': self._z_score_to_percentile(z_score)
            }
            
        except Exception:
            return {
                'level': 'خطأ',
                'significance': 'غير محدد',
                'direction': 'غير محدد',
                'probability_mean_reversion': 0.5
            }
    
    def _z_score_to_percentile(self, z_score: float) -> float:
        """
        تحويل Z-Score إلى مئين
        
        Args:
            z_score: قيمة Z-Score
            
        Returns:
            المئين المقابل
        """
        try:
            # تقريب بسيط لتحويل Z-Score إلى مئين
            if z_score >= 2.0:
                return 97.7
            elif z_score >= 1.5:
                return 93.3
            elif z_score >= 1.0:
                return 84.1
            elif z_score >= 0.5:
                return 69.1
            elif z_score >= 0:
                return 50.0 + (z_score * 19.1)
            elif z_score >= -0.5:
                return 50.0 + (z_score * 19.1)
            elif z_score >= -1.0:
                return 30.9
            elif z_score >= -1.5:
                return 15.9
            elif z_score >= -2.0:
                return 6.7
            else:
                return 2.3
        except Exception:
            return 50.0
    
    def _analyze_z_score_trend(self, z_score_series: pd.Series) -> Dict[str, Any]:
        """
        تحليل اتجاه Z-Score
        
        Args:
            z_score_series: سلسلة Z-Score
            
        Returns:
            تحليل الاتجاه
        """
        try:
            if len(z_score_series) < 5:
                return {'direction': 'غير محدد', 'strength': 0.0}
            
            recent_z_scores = z_score_series.tail(5).dropna()
            if len(recent_z_scores) < 3:
                return {'direction': 'غير محدد', 'strength': 0.0}
            
            # حساب الاتجاه العام
            slope = (recent_z_scores.iloc[-1] - recent_z_scores.iloc[0]) / len(recent_z_scores)
            
            # تحديد الاتجاه
            if slope > 0.1:
                direction = 'متزايد'
                strength = min(abs(slope) * 2, 1.0)
            elif slope < -0.1:
                direction = 'متناقص'
                strength = min(abs(slope) * 2, 1.0)
            else:
                direction = 'مستقر'
                strength = 0.0
            
            return {
                'direction': direction,
                'strength': strength,
                'slope': slope
            }
            
        except Exception:
            return {'direction': 'خطأ', 'strength': 0.0}
    
    def _analyze_saturation(self, z_score: float, z_score_change: float) -> Dict[str, Any]:
        """
        تحليل حالات التشبع
        
        Args:
            z_score: Z-Score الحالي
            z_score_change: التغيير في Z-Score
            
        Returns:
            تحليل التشبع
        """
        try:
            # تحديد حالة التشبع
            if z_score >= self.extreme_threshold:
                saturation = 'تشبع شرائي شديد'
                action = 'بيع محتمل'
                urgency = 'عالي'
            elif z_score >= self.moderate_threshold:
                saturation = 'تشبع شرائي متوسط'
                action = 'مراقبة للبيع'
                urgency = 'متوسط'
            elif z_score <= -self.extreme_threshold:
                saturation = 'تشبع بيعي شديد'
                action = 'شراء محتمل'
                urgency = 'عالي'
            elif z_score <= -self.moderate_threshold:
                saturation = 'تشبع بيعي متوسط'
                action = 'مراقبة للشراء'
                urgency = 'متوسط'
            else:
                saturation = 'لا يوجد تشبع'
                action = 'انتظار'
                urgency = 'منخفض'
            
            # تحليل التغيير
            if abs(z_score_change) > 0.5:
                change_analysis = 'تغيير سريع'
                momentum = 'قوي'
            elif abs(z_score_change) > 0.2:
                change_analysis = 'تغيير متوسط'
                momentum = 'متوسط'
            else:
                change_analysis = 'تغيير بطيء'
                momentum = 'ضعيف'
            
            return {
                'saturation': saturation,
                'action': action,
                'urgency': urgency,
                'change_analysis': change_analysis,
                'momentum': momentum,
                'z_score_change': z_score_change
            }
            
        except Exception:
            return {
                'saturation': 'خطأ',
                'action': 'غير محدد',
                'urgency': 'منخفض'
            }
    
    def _analyze_reversal_probability(self, current_z: float, previous_z: float, z_series: pd.Series) -> Dict[str, Any]:
        """
        تحليل احتمالية الانعكاس
        
        Args:
            current_z: Z-Score الحالي
            previous_z: Z-Score السابق
            z_series: سلسلة Z-Score
            
        Returns:
            تحليل احتمالية الانعكاس
        """
        try:
            # تحليل التغيير في الاتجاه
            if abs(current_z) > abs(previous_z) and abs(current_z) > self.moderate_threshold:
                reversal_probability = min(abs(current_z) / self.extreme_threshold * 0.8, 0.9)
                reversal_type = 'احتمالية عالية للانعكاس'
            elif abs(current_z) < abs(previous_z) and abs(previous_z) > self.moderate_threshold:
                reversal_probability = 0.6
                reversal_type = 'بداية انعكاس محتمل'
            elif abs(current_z) > self.extreme_threshold:
                reversal_probability = 0.85
                reversal_type = 'انعكاس شديد الاحتمال'
            else:
                reversal_probability = 0.3
                reversal_type = 'احتمالية منخفضة للانعكاس'
            
            # تحديد اتجاه الانعكاس المتوقع
            if current_z > self.moderate_threshold:
                expected_direction = 'انعكاس هبوطي'
            elif current_z < -self.moderate_threshold:
                expected_direction = 'انعكاس صعودي'
            else:
                expected_direction = 'غير محدد'
            
            return {
                'probability': reversal_probability,
                'type': reversal_type,
                'expected_direction': expected_direction,
                'current_z': current_z,
                'previous_z': previous_z
            }
            
        except Exception:
            return {
                'probability': 0.5,
                'type': 'خطأ',
                'expected_direction': 'غير محدد'
            }
    
    def _calculate_statistical_levels(self, mean: float, std: float) -> Dict[str, Any]:
        """
        حساب المستويات الإحصائية
        
        Args:
            mean: المتوسط
            std: الانحراف المعياري
            
        Returns:
            المستويات الإحصائية
        """
        try:
            levels = {
                'mean': mean,
                'upper_1_std': mean + std,
                'upper_2_std': mean + (2 * std),
                'upper_3_std': mean + (3 * std),
                'lower_1_std': mean - std,
                'lower_2_std': mean - (2 * std),
                'lower_3_std': mean - (3 * std),
                'resistance_moderate': mean + (self.moderate_threshold * std),
                'resistance_extreme': mean + (self.extreme_threshold * std),
                'support_moderate': mean - (self.moderate_threshold * std),
                'support_extreme': mean - (self.extreme_threshold * std)
            }
            
            return levels
            
        except Exception:
            return {'mean': mean}
    
    def _analyze_volatility(self, current_std: float, std_series: pd.Series) -> Dict[str, Any]:
        """
        تحليل التقلبات
        
        Args:
            current_std: الانحراف المعياري الحالي
            std_series: سلسلة الانحراف المعياري
            
        Returns:
            تحليل التقلبات
        """
        try:
            if len(std_series) < 10:
                return {'level': 'غير محدد', 'trend': 'غير محدد'}
            
            recent_std = std_series.tail(10).dropna()
            avg_std = recent_std.mean()
            
            # مقارنة التقلبات الحالية بالمتوسط
            if current_std > avg_std * 1.5:
                volatility_level = 'عالي'
            elif current_std > avg_std * 1.2:
                volatility_level = 'فوق المتوسط'
            elif current_std < avg_std * 0.8:
                volatility_level = 'منخفض'
            else:
                volatility_level = 'متوسط'
            
            # اتجاه التقلبات
            std_slope = (recent_std.iloc[-1] - recent_std.iloc[0]) / len(recent_std)
            if std_slope > 0.001:
                volatility_trend = 'متزايد'
            elif std_slope < -0.001:
                volatility_trend = 'متناقص'
            else:
                volatility_trend = 'مستقر'
            
            return {
                'level': volatility_level,
                'trend': volatility_trend,
                'current_std': current_std,
                'avg_std': avg_std,
                'ratio': current_std / avg_std if avg_std != 0 else 1.0
            }
            
        except Exception:
            return {'level': 'خطأ', 'trend': 'خطأ'}
    
    def get_signal(self, current_value: Dict[str, Any], previous_values: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        تحليل إشارة Z-Score
        
        Args:
            current_value: القيمة الحالية
            previous_values: القيم السابقة
            
        Returns:
            تحليل الإشارة
        """
        try:
            # استخراج البيانات
            z_score = current_value.get('z_score', 0)
            deviation_analysis = current_value.get('deviation_analysis', {})
            saturation_analysis = current_value.get('saturation_analysis', {})
            reversal_analysis = current_value.get('reversal_analysis', {})
            
            # تحليل الإشارة الأساسية بناء على Z-Score
            direction = "NEUTRAL"
            base_strength = 0.1
            
            # إشارات التشبع والانعكاس
            if z_score >= self.extreme_threshold:
                direction = "PUT"  # تشبع شرائي شديد - بيع
                base_strength = 0.8
            elif z_score <= -self.extreme_threshold:
                direction = "CALL"  # تشبع بيعي شديد - شراء
                base_strength = 0.8
            elif z_score >= self.moderate_threshold:
                direction = "PUT"  # تشبع شرائي متوسط
                base_strength = 0.6
            elif z_score <= -self.moderate_threshold:
                direction = "CALL"  # تشبع بيعي متوسط
                base_strength = 0.6
            
            # تعديل القوة حسب احتمالية الانعكاس
            reversal_probability = reversal_analysis.get('probability', 0.5)
            strength_multiplier = 1.0 + (reversal_probability - 0.5)
            
            # تعديل القوة حسب مستوى الانحراف
            significance = deviation_analysis.get('significance', 'منخفض')
            if significance == 'عالي جداً':
                strength_multiplier += 0.3
            elif significance == 'عالي':
                strength_multiplier += 0.2
            
            # حساب القوة النهائية
            final_strength = min(base_strength * strength_multiplier, 1.0)
            
            # حساب مستوى الثقة
            confidence = final_strength
            
            # زيادة الثقة مع الانحرافات الشديدة
            if abs(z_score) >= self.extreme_threshold:
                confidence *= 1.2
            
            confidence = min(confidence, 1.0)
            
            # وزن Z-Score متوسط إلى عالي
            weight = 1.0
            
            signal = {
                'direction': direction,
                'strength': final_strength,
                'confidence': confidence,
                'weight': weight,
                'details': {
                    'z_score': z_score,
                    'deviation_level': deviation_analysis.get('level', 'غير محدد'),
                    'saturation': saturation_analysis.get('saturation', 'غير محدد'),
                    'reversal_probability': reversal_probability,
                    'expected_reversal': reversal_analysis.get('expected_direction', 'غير محدد'),
                    'statistical_levels': current_value.get('statistical_levels', {}),
                    'percentile': deviation_analysis.get('percentile', 50)
                },
                'reasoning': self._get_signal_reasoning(direction, current_value),
                'timestamp': datetime.now()
            }
            
            return signal
            
        except Exception as e:
            logger.error(f"خطأ في تحليل إشارة Z-Score: {str(e)}")
            return {
                'direction': 'NEUTRAL',
                'strength': 0.0,
                'confidence': 0.0,
                'weight': 1.0,
                'error': str(e)
            }
    
    def _get_signal_reasoning(self, direction: str, current_value: Dict[str, Any]) -> str:
        """
        الحصول على تفسير الإشارة
        
        Args:
            direction: اتجاه الإشارة
            current_value: القيمة الحالية
            
        Returns:
            تفسير نصي للإشارة
        """
        z_score = current_value.get('z_score', 0)
        deviation_level = current_value.get('deviation_analysis', {}).get('level', 'غير محدد')
        saturation = current_value.get('saturation_analysis', {}).get('saturation', 'غير محدد')
        
        if direction == "CALL":
            return f"Z-Score: {z_score:.2f} - {deviation_level} مع {saturation} - إشارة شراء"
        elif direction == "PUT":
            return f"Z-Score: {z_score:.2f} - {deviation_level} مع {saturation} - إشارة بيع"
        else:
            return f"Z-Score: {z_score:.2f} - {deviation_level} - إشارة محايدة"
    
    def get_minimum_periods(self) -> int:
        """الحد الأدنى من الفترات المطلوبة"""
        return max(self.period * 2, 40)  # ضعف الفترة على الأقل
