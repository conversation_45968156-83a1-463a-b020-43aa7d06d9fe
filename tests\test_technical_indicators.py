"""
🧪 اختبار شامل للمؤشرات الفنية الـ15
يختبر جميع المؤشرات الفنية مع البيانات الحقيقية
"""

import sys
import os
import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from technical_analysis import *
from config.settings import Settings
from config.database import DatabaseConfig
from database.repositories import CandleRepository
from data_collector.pocket_option_connector import PocketOptionConnector

# إعداد السجلات
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class TechnicalIndicatorsTest:
    """كلاس اختبار شامل للمؤشرات الفنية"""
    
    def __init__(self):
        """تهيئة الاختبار"""
        self.settings = Settings()
        self.db_config = DatabaseConfig()
        self.candle_repo = CandleRepository()
        self.indicators_engine = IndicatorsEngine()
        
        # تسجيل جميع المؤشرات
        self.register_all_indicators()
        
        # إحصائيات الاختبار
        self.test_results = {}
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        
        logger.info("تم تهيئة اختبار المؤشرات الفنية")
    
    def register_all_indicators(self):
        """تسجيل جميع المؤشرات الـ15 في المحرك"""
        try:
            # مؤشرات EMA
            self.indicators_engine.register_indicator(EMA5())
            self.indicators_engine.register_indicator(EMA10())
            self.indicators_engine.register_indicator(EMA21())
            
            # مؤشر SMA
            self.indicators_engine.register_indicator(SMA10())
            
            # مؤشرات RSI
            self.indicators_engine.register_indicator(RSI5())
            self.indicators_engine.register_indicator(RSI14())
            
            # مؤشر MACD
            self.indicators_engine.register_indicator(MACDIndicator())
            
            # مؤشر Momentum
            self.indicators_engine.register_indicator(Momentum10())
            
            # مؤشر Bollinger Bands
            self.indicators_engine.register_indicator(BollingerBandsIndicator())
            
            # مؤشرات ATR
            self.indicators_engine.register_indicator(ATR5())
            self.indicators_engine.register_indicator(ATR14())
            
            # مؤشر Heiken Ashi
            self.indicators_engine.register_indicator(HeikenAshiIndicator())
            
            # مؤشر Z-Score
            self.indicators_engine.register_indicator(ZScoreIndicator())
            
            logger.info(f"تم تسجيل {len(self.indicators_engine.indicators)} مؤشر فني")
            
        except Exception as e:
            logger.error(f"خطأ في تسجيل المؤشرات: {str(e)}")
            raise
    
    def generate_sample_data(self, num_candles: int = 100) -> pd.DataFrame:
        """إنشاء بيانات عينة للاختبار"""
        try:
            # إنشاء بيانات عشوائية واقعية
            np.random.seed(42)  # للحصول على نتائج ثابتة
            
            dates = pd.date_range(start=datetime.now() - timedelta(days=num_candles), 
                                periods=num_candles, freq='5T')
            
            # بدء بسعر أساسي
            base_price = 1.2000
            prices = [base_price]
            
            # إنشاء حركة سعرية واقعية
            for i in range(1, num_candles):
                # تغيير عشوائي صغير
                change = np.random.normal(0, 0.0005)
                new_price = prices[-1] + change
                prices.append(max(new_price, 0.5))  # منع الأسعار السالبة
            
            # إنشاء OHLC من الأسعار
            data = []
            for i, price in enumerate(prices):
                # إنشاء تقلبات داخل الشمعة
                volatility = np.random.uniform(0.0001, 0.0010)
                
                open_price = price
                close_price = price + np.random.normal(0, volatility/2)
                high_price = max(open_price, close_price) + np.random.uniform(0, volatility)
                low_price = min(open_price, close_price) - np.random.uniform(0, volatility)
                
                data.append({
                    'timestamp': dates[i],
                    'open': round(open_price, 5),
                    'high': round(high_price, 5),
                    'low': round(low_price, 5),
                    'close': round(close_price, 5),
                    'volume': np.random.randint(100, 1000)
                })
            
            df = pd.DataFrame(data)
            logger.info(f"تم إنشاء {len(df)} شمعة للاختبار")
            return df
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء البيانات: {str(e)}")
            raise
    
    async def get_real_data(self, pair_symbol: str = "EURUSD") -> pd.DataFrame:
        """جلب بيانات حقيقية من قاعدة البيانات"""
        try:
            # محاولة جلب البيانات من قاعدة البيانات
            candles = await self.candle_repo.get_recent_candles(pair_symbol, limit=150)
            
            if candles and len(candles) >= 50:
                data = []
                for candle in candles:
                    data.append({
                        'timestamp': candle.timestamp,
                        'open': float(candle.open_price),
                        'high': float(candle.high_price),
                        'low': float(candle.low_price),
                        'close': float(candle.close_price),
                        'volume': float(candle.volume) if candle.volume else 0
                    })
                
                df = pd.DataFrame(data)
                logger.info(f"تم جلب {len(df)} شمعة حقيقية من قاعدة البيانات")
                return df
            else:
                logger.warning("لا توجد بيانات كافية في قاعدة البيانات، سيتم استخدام بيانات عينة")
                return self.generate_sample_data()
                
        except Exception as e:
            logger.warning(f"خطأ في جلب البيانات الحقيقية: {str(e)}")
            logger.info("سيتم استخدام بيانات عينة")
            return self.generate_sample_data()
    
    def test_single_indicator(self, indicator_name: str, data: pd.DataFrame) -> bool:
        """اختبار مؤشر واحد"""
        try:
            logger.info(f"اختبار المؤشر: {indicator_name}")
            
            # حساب المؤشر
            result = self.indicators_engine.calculate_single_indicator(indicator_name, data)
            
            if result is None:
                logger.error(f"فشل في حساب المؤشر {indicator_name}")
                return False
            
            # التحقق من وجود القيم المطلوبة
            if not result.values:
                logger.error(f"المؤشر {indicator_name} لا يحتوي على قيم")
                return False
            
            # التحقق من صحة الإشارة
            signal = result.signal
            if not signal or 'direction' not in signal:
                logger.error(f"المؤشر {indicator_name} لا يحتوي على إشارة صحيحة")
                return False
            
            # التحقق من صحة اتجاه الإشارة
            valid_directions = ['CALL', 'PUT', 'NEUTRAL']
            if signal['direction'] not in valid_directions:
                logger.error(f"المؤشر {indicator_name} يحتوي على اتجاه غير صحيح: {signal['direction']}")
                return False
            
            # التحقق من قيم القوة والثقة
            strength = signal.get('strength', 0)
            confidence = signal.get('confidence', 0)
            
            if not (0 <= strength <= 1):
                logger.error(f"المؤشر {indicator_name} يحتوي على قوة غير صحيحة: {strength}")
                return False
            
            if not (0 <= confidence <= 1):
                logger.error(f"المؤشر {indicator_name} يحتوي على ثقة غير صحيحة: {confidence}")
                return False
            
            # تسجيل النتائج
            self.test_results[indicator_name] = {
                'status': 'نجح',
                'values_count': len(result.values),
                'direction': signal['direction'],
                'strength': strength,
                'confidence': confidence,
                'reasoning': signal.get('reasoning', 'غير متوفر')
            }
            
            logger.info(f"✅ نجح اختبار {indicator_name}: {signal['direction']} بقوة {strength:.2f}")
            return True
            
        except Exception as e:
            logger.error(f"❌ فشل اختبار {indicator_name}: {str(e)}")
            self.test_results[indicator_name] = {
                'status': 'فشل',
                'error': str(e)
            }
            return False
    
    async def test_all_indicators(self) -> dict:
        """اختبار جميع المؤشرات"""
        try:
            logger.info("🚀 بدء اختبار جميع المؤشرات الفنية الـ15")
            
            # جلب البيانات
            data = await self.get_real_data()
            
            if data.empty:
                raise ValueError("لا توجد بيانات للاختبار")
            
            logger.info(f"📊 البيانات المستخدمة: {len(data)} شمعة من {data['timestamp'].min()} إلى {data['timestamp'].max()}")
            
            # اختبار كل مؤشر على حدة
            for indicator_name in self.indicators_engine.indicators.keys():
                self.total_tests += 1
                if self.test_single_indicator(indicator_name, data):
                    self.passed_tests += 1
                else:
                    self.failed_tests += 1
            
            # اختبار الحساب المتوازي
            logger.info("🔄 اختبار الحساب المتوازي لجميع المؤشرات")
            all_results = self.indicators_engine.calculate_all_indicators(data)
            
            # اختبار الإشارة الموحدة
            if all_results:
                combined_signal = self.indicators_engine.get_combined_signal(all_results)
                logger.info(f"📈 الإشارة الموحدة: {combined_signal.get('direction', 'غير محدد')} بقوة {combined_signal.get('strength', 0):.2f}")
            
            # إحصائيات الأداء
            performance_stats = self.indicators_engine.get_performance_stats()
            
            # تجميع النتائج النهائية
            final_results = {
                'test_summary': {
                    'total_indicators': len(self.indicators_engine.indicators),
                    'total_tests': self.total_tests,
                    'passed_tests': self.passed_tests,
                    'failed_tests': self.failed_tests,
                    'success_rate': (self.passed_tests / self.total_tests) * 100 if self.total_tests > 0 else 0
                },
                'individual_results': self.test_results,
                'combined_signal': combined_signal if all_results else None,
                'performance_stats': performance_stats,
                'data_info': {
                    'candles_count': len(data),
                    'date_range': f"{data['timestamp'].min()} - {data['timestamp'].max()}",
                    'price_range': f"{data['low'].min():.5f} - {data['high'].max():.5f}"
                }
            }
            
            return final_results
            
        except Exception as e:
            logger.error(f"خطأ في اختبار المؤشرات: {str(e)}")
            raise
    
    def print_test_results(self, results: dict):
        """طباعة نتائج الاختبار"""
        try:
            print("\n" + "="*80)
            print("🎯 نتائج اختبار المؤشرات الفنية الـ15")
            print("="*80)
            
            # ملخص الاختبار
            summary = results['test_summary']
            print(f"\n📊 ملخص الاختبار:")
            print(f"   • إجمالي المؤشرات: {summary['total_indicators']}")
            print(f"   • إجمالي الاختبارات: {summary['total_tests']}")
            print(f"   • الاختبارات الناجحة: {summary['passed_tests']}")
            print(f"   • الاختبارات الفاشلة: {summary['failed_tests']}")
            print(f"   • معدل النجاح: {summary['success_rate']:.1f}%")
            
            # النتائج الفردية
            print(f"\n📈 نتائج المؤشرات الفردية:")
            for indicator_name, result in results['individual_results'].items():
                status_icon = "✅" if result['status'] == 'نجح' else "❌"
                print(f"   {status_icon} {indicator_name}: {result['status']}")
                
                if result['status'] == 'نجح':
                    print(f"      الاتجاه: {result['direction']} | القوة: {result['strength']:.2f} | الثقة: {result['confidence']:.2f}")
                else:
                    print(f"      الخطأ: {result.get('error', 'غير محدد')}")
            
            # الإشارة الموحدة
            if results.get('combined_signal'):
                signal = results['combined_signal']
                print(f"\n🎯 الإشارة الموحدة:")
                print(f"   الاتجاه: {signal.get('direction', 'غير محدد')}")
                print(f"   القوة: {signal.get('strength', 0):.2f}")
                print(f"   الثقة: {signal.get('confidence', 0):.2f}")
                print(f"   عدد الإشارات: {signal.get('signal_count', {})}")
            
            # إحصائيات الأداء
            if results.get('performance_stats'):
                stats = results['performance_stats']
                print(f"\n⚡ إحصائيات الأداء:")
                print(f"   متوسط وقت الحساب: {stats.get('average_calculation_time', 0):.3f} ثانية")
                print(f"   المؤشرات المسجلة: {stats.get('registered_indicators', 0)}")
            
            print("\n" + "="*80)
            
        except Exception as e:
            logger.error(f"خطأ في طباعة النتائج: {str(e)}")


async def main():
    """الدالة الرئيسية للاختبار"""
    try:
        # إنشاء كائن الاختبار
        test = TechnicalIndicatorsTest()
        
        # تشغيل الاختبار
        results = await test.test_all_indicators()
        
        # طباعة النتائج
        test.print_test_results(results)
        
        # تحديد نجاح الاختبار
        success_rate = results['test_summary']['success_rate']
        
        if success_rate == 100:
            print("🎉 جميع المؤشرات تعمل بشكل مثالي!")
            return True
        elif success_rate >= 80:
            print(f"✅ معظم المؤشرات تعمل بشكل جيد ({success_rate:.1f}%)")
            return True
        else:
            print(f"⚠️ بعض المؤشرات تحتاج إصلاح ({success_rate:.1f}%)")
            return False
            
    except Exception as e:
        logger.error(f"خطأ في الاختبار الرئيسي: {str(e)}")
        print(f"❌ فشل الاختبار: {str(e)}")
        return False


if __name__ == "__main__":
    # تشغيل الاختبار
    success = asyncio.run(main())
    
    # إنهاء البرنامج مع رمز الحالة المناسب
    exit(0 if success else 1)
