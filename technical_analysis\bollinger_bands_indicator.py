"""
📊 مؤشر نطاقات بولينجر (Bollinger Bands)
يحسب Bollinger Bands(20,2) مع تحليل مناطق التشبع والانضغاط
"""

from typing import Dict, List, Any
import pandas as pd
import numpy as np
from datetime import datetime
import logging

from .base_indicator import BaseIndicator
from config.constants import TechnicalIndicators

logger = logging.getLogger(__name__)


class BollingerBandsIndicator(BaseIndicator):
    """
    مؤشر نطاقات بولينجر (Bollinger Bands)
    يحسب النطاقات العلوية والسفلية ويحلل مناطق التشبع والانضغاط
    """
    
    def __init__(self, period: int = 20, std_dev: float = 2.0, **kwargs):
        """
        تهيئة مؤشر Bollinger Bands
        
        Args:
            period: فترة المتوسط المتحرك (افتراضي 20)
            std_dev: عدد الانحرافات المعيارية (افتراضي 2.0)
            **kwargs: معاملات إضافية
        """
        super().__init__(TechnicalIndicators.BOLLINGER_BANDS, period=period, std=std_dev, **kwargs)
        self.period = period
        self.std_dev = std_dev
        
        logger.info(f"تم تهيئة مؤشر Bollinger Bands({period},{std_dev})")
    
    def calculate(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        حساب قيم Bollinger Bands
        
        Args:
            data: بيانات الشموع مع أعمدة OHLC
            
        Returns:
            قاموس يحتوي على قيم Bollinger Bands المحسوبة
        """
        try:
            if not self.validate_data(data):
                raise ValueError("البيانات غير صحيحة")
            
            close_prices = data['close'].astype(float)
            high_prices = data['high'].astype(float)
            low_prices = data['low'].astype(float)
            
            # حساب المتوسط المتحرك (الخط الأوسط)
            middle_band = close_prices.rolling(window=self.period).mean()
            
            # حساب الانحراف المعياري
            std_deviation = close_prices.rolling(window=self.period).std()
            
            # حساب النطاقات العلوية والسفلية
            upper_band = middle_band + (std_deviation * self.std_dev)
            lower_band = middle_band - (std_deviation * self.std_dev)
            
            # القيم الحالية
            current_price = float(close_prices.iloc[-1])
            current_middle = float(middle_band.iloc[-1])
            current_upper = float(upper_band.iloc[-1])
            current_lower = float(lower_band.iloc[-1])
            current_std = float(std_deviation.iloc[-1])
            
            # حساب عرض النطاق
            band_width = current_upper - current_lower
            band_width_percentage = (band_width / current_middle) * 100 if current_middle != 0 else 0
            
            # حساب موقع السعر داخل النطاق (%B)
            percent_b = ((current_price - current_lower) / (current_upper - current_lower)) * 100 if (current_upper - current_lower) != 0 else 50
            
            # تحليل موقع السعر
            position_analysis = self._analyze_price_position(current_price, current_upper, current_middle, current_lower, percent_b)
            
            # تحليل انضغاط النطاقات
            squeeze_analysis = self._analyze_band_squeeze(std_deviation, band_width_percentage)
            
            # تحليل الاتجاه
            trend_analysis = self._analyze_trend(middle_band, upper_band, lower_band)
            
            # كشف الارتدادات
            bounce_analysis = self._detect_bounces(close_prices, upper_band, lower_band)
            
            # حساب قوة الإشارة
            signal_strength = self._calculate_signal_strength(position_analysis, squeeze_analysis, bounce_analysis)
            
            # حساب مستويات الدعم والمقاومة الديناميكية
            support_resistance = self._calculate_dynamic_levels(current_upper, current_middle, current_lower, percent_b)
            
            result = {
                'upper_band': current_upper,
                'middle_band': current_middle,
                'lower_band': current_lower,
                'current_price': current_price,
                'std_deviation': current_std,
                'band_width': band_width,
                'band_width_percentage': band_width_percentage,
                'percent_b': percent_b,
                'position_analysis': position_analysis,
                'squeeze_analysis': squeeze_analysis,
                'trend_analysis': trend_analysis,
                'bounce_analysis': bounce_analysis,
                'signal_strength': signal_strength,
                'support_resistance': support_resistance,
                'period': self.period,
                'std_dev_multiplier': self.std_dev,
                'calculation_time': datetime.now()
            }
            
            # تحديث التخزين المؤقت
            self.update_cache('last_calculation', result)
            
            return result
            
        except Exception as e:
            logger.error(f"خطأ في حساب Bollinger Bands: {str(e)}")
            raise
    
    def _analyze_price_position(self, price: float, upper: float, middle: float, lower: float, percent_b: float) -> Dict[str, Any]:
        """
        تحليل موقع السعر داخل النطاقات
        
        Args:
            price: السعر الحالي
            upper, middle, lower: النطاقات
            percent_b: موقع السعر النسبي
            
        Returns:
            تحليل الموقع
        """
        try:
            # تحديد المنطقة
            if percent_b > 100:
                zone = "خارج النطاق العلوي"
                zone_strength = min((percent_b - 100) / 20, 1.0)
            elif percent_b < 0:
                zone = "خارج النطاق السفلي"
                zone_strength = min(abs(percent_b) / 20, 1.0)
            elif percent_b > 80:
                zone = "قريب من النطاق العلوي"
                zone_strength = (percent_b - 80) / 20
            elif percent_b < 20:
                zone = "قريب من النطاق السفلي"
                zone_strength = (20 - percent_b) / 20
            elif 40 <= percent_b <= 60:
                zone = "قريب من الوسط"
                zone_strength = 0.0
            else:
                zone = "داخل النطاق"
                zone_strength = 0.3
            
            # تحديد المسافة من الحدود
            distance_to_upper = upper - price
            distance_to_lower = price - lower
            distance_to_middle = abs(price - middle)
            
            # تحديد أقرب مستوى
            if distance_to_upper < distance_to_lower and distance_to_upper < distance_to_middle:
                nearest_level = "النطاق العلوي"
                nearest_distance = distance_to_upper
            elif distance_to_lower < distance_to_middle:
                nearest_level = "النطاق السفلي"
                nearest_distance = distance_to_lower
            else:
                nearest_level = "الخط الأوسط"
                nearest_distance = distance_to_middle
            
            return {
                'zone': zone,
                'zone_strength': zone_strength,
                'percent_b': percent_b,
                'nearest_level': nearest_level,
                'nearest_distance': nearest_distance,
                'distance_to_upper': distance_to_upper,
                'distance_to_lower': distance_to_lower,
                'distance_to_middle': distance_to_middle
            }
            
        except Exception:
            return {
                'zone': 'خطأ',
                'zone_strength': 0.0,
                'percent_b': 50.0,
                'nearest_level': 'غير محدد',
                'nearest_distance': 0.0
            }
    
    def _analyze_band_squeeze(self, std_series: pd.Series, band_width_pct: float) -> Dict[str, Any]:
        """
        تحليل انضغاط النطاقات
        
        Args:
            std_series: سلسلة الانحراف المعياري
            band_width_pct: عرض النطاق النسبي
            
        Returns:
            تحليل الانضغاط
        """
        try:
            if len(std_series) < 20:
                return {'status': 'غير كافي', 'strength': 0.0, 'duration': 0}
            
            # حساب متوسط عرض النطاق للفترات السابقة
            recent_std = std_series.tail(20)
            avg_std = recent_std.mean()
            current_std = std_series.iloc[-1]
            
            # تحديد حالة الانضغاط
            if current_std < avg_std * 0.7:
                status = 'انضغاط قوي'
                strength = min((avg_std - current_std) / avg_std * 2, 1.0)
            elif current_std < avg_std * 0.85:
                status = 'انضغاط متوسط'
                strength = min((avg_std - current_std) / avg_std * 1.5, 1.0)
            elif current_std > avg_std * 1.3:
                status = 'توسع قوي'
                strength = min((current_std - avg_std) / avg_std, 1.0)
            elif current_std > avg_std * 1.15:
                status = 'توسع متوسط'
                strength = min((current_std - avg_std) / avg_std * 0.8, 1.0)
            else:
                status = 'طبيعي'
                strength = 0.0
            
            # حساب مدة الانضغاط
            squeeze_duration = 0
            if 'انضغاط' in status:
                for i in range(len(recent_std)):
                    if recent_std.iloc[-(i+1)] < avg_std * 0.85:
                        squeeze_duration += 1
                    else:
                        break
            
            return {
                'status': status,
                'strength': strength,
                'duration': squeeze_duration,
                'current_std': current_std,
                'avg_std': avg_std,
                'band_width_pct': band_width_pct
            }
            
        except Exception:
            return {'status': 'خطأ', 'strength': 0.0, 'duration': 0}
    
    def _analyze_trend(self, middle: pd.Series, upper: pd.Series, lower: pd.Series) -> Dict[str, Any]:
        """
        تحليل اتجاه النطاقات
        
        Args:
            middle, upper, lower: سلاسل النطاقات
            
        Returns:
            تحليل الاتجاه
        """
        try:
            if len(middle) < 5:
                return {'direction': 'غير محدد', 'strength': 0.0}
            
            recent_middle = middle.tail(5)
            slope = (recent_middle.iloc[-1] - recent_middle.iloc[0]) / 4
            
            if slope > 0.001:
                direction = 'صاعد'
                strength = min(abs(slope) * 1000, 1.0)
            elif slope < -0.001:
                direction = 'هابط'
                strength = min(abs(slope) * 1000, 1.0)
            else:
                direction = 'جانبي'
                strength = 0.0
            
            return {
                'direction': direction,
                'strength': strength,
                'slope': slope
            }
            
        except Exception:
            return {'direction': 'خطأ', 'strength': 0.0, 'slope': 0.0}
    
    def _detect_bounces(self, prices: pd.Series, upper: pd.Series, lower: pd.Series) -> Dict[str, Any]:
        """
        كشف الارتدادات من النطاقات
        
        Args:
            prices: أسعار الإغلاق
            upper, lower: النطاقات العلوية والسفلية
            
        Returns:
            تحليل الارتدادات
        """
        try:
            if len(prices) < 5:
                return {'type': 'غير كافي', 'strength': 0.0}
            
            recent_prices = prices.tail(5)
            recent_upper = upper.tail(5)
            recent_lower = lower.tail(5)
            
            current_price = recent_prices.iloc[-1]
            previous_price = recent_prices.iloc[-2]
            
            # كشف الارتداد من النطاق العلوي
            if (previous_price >= recent_upper.iloc[-2] and 
                current_price < recent_upper.iloc[-1]):
                bounce_type = 'ارتداد من النطاق العلوي'
                strength = min(abs(previous_price - current_price) / current_price * 100, 1.0)
            
            # كشف الارتداد من النطاق السفلي
            elif (previous_price <= recent_lower.iloc[-2] and 
                  current_price > recent_lower.iloc[-1]):
                bounce_type = 'ارتداد من النطاق السفلي'
                strength = min(abs(current_price - previous_price) / current_price * 100, 1.0)
            
            else:
                bounce_type = 'لا يوجد'
                strength = 0.0
            
            return {
                'type': bounce_type,
                'strength': strength
            }
            
        except Exception:
            return {'type': 'خطأ', 'strength': 0.0}
    
    def _calculate_signal_strength(self, position: Dict[str, Any], squeeze: Dict[str, Any], bounce: Dict[str, Any]) -> float:
        """
        حساب قوة الإشارة الإجمالية
        
        Args:
            position, squeeze, bounce: تحليلات مختلفة
            
        Returns:
            قوة الإشارة (0-1)
        """
        strength = 0.0
        
        # قوة الموقع
        strength += position.get('zone_strength', 0) * 0.4
        
        # قوة الانضغاط/التوسع
        strength += squeeze.get('strength', 0) * 0.3
        
        # قوة الارتداد
        strength += bounce.get('strength', 0) * 0.3
        
        return min(strength, 1.0)
    
    def _calculate_dynamic_levels(self, upper: float, middle: float, lower: float, percent_b: float) -> Dict[str, Any]:
        """
        حساب مستويات الدعم والمقاومة الديناميكية
        
        Args:
            upper, middle, lower: النطاقات
            percent_b: الموقع النسبي
            
        Returns:
            المستويات الديناميكية
        """
        try:
            # تحديد مستويات الدعم والمقاومة
            if percent_b > 50:
                # السعر في النصف العلوي
                resistance = upper
                support = middle
                next_target = upper + (upper - middle) * 0.5  # هدف خارج النطاق
            else:
                # السعر في النصف السفلي
                resistance = middle
                support = lower
                next_target = lower - (middle - lower) * 0.5  # هدف خارج النطاق
            
            return {
                'resistance': resistance,
                'support': support,
                'next_target': next_target,
                'middle_line': middle
            }
            
        except Exception:
            return {
                'resistance': upper,
                'support': lower,
                'next_target': middle,
                'middle_line': middle
            }
    
    def get_signal(self, current_value: Dict[str, Any], previous_values: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        تحليل إشارة Bollinger Bands
        
        Args:
            current_value: القيمة الحالية
            previous_values: القيم السابقة
            
        Returns:
            تحليل الإشارة
        """
        try:
            # استخراج البيانات
            position = current_value.get('position_analysis', {})
            squeeze = current_value.get('squeeze_analysis', {})
            bounce = current_value.get('bounce_analysis', {})
            trend = current_value.get('trend_analysis', {})
            percent_b = current_value.get('percent_b', 50)
            
            # تحليل الإشارة الأساسية
            direction = "NEUTRAL"
            base_strength = 0.1
            
            # إشارات الارتداد
            bounce_type = bounce.get('type', 'لا يوجد')
            if bounce_type == 'ارتداد من النطاق السفلي':
                direction = "CALL"
                base_strength = 0.8
            elif bounce_type == 'ارتداد من النطاق العلوي':
                direction = "PUT"
                base_strength = 0.8
            
            # إشارات التشبع
            elif percent_b < 20:  # قريب من النطاق السفلي
                direction = "CALL"
                base_strength = 0.6
            elif percent_b > 80:  # قريب من النطاق العلوي
                direction = "PUT"
                base_strength = 0.6
            
            # إشارات الاختراق
            elif percent_b > 100:  # خارج النطاق العلوي
                if trend.get('direction') == 'صاعد':
                    direction = "CALL"  # استمرار الاتجاه
                    base_strength = 0.5
                else:
                    direction = "PUT"  # ارتداد محتمل
                    base_strength = 0.4
            elif percent_b < 0:  # خارج النطاق السفلي
                if trend.get('direction') == 'هابط':
                    direction = "PUT"  # استمرار الاتجاه
                    base_strength = 0.5
                else:
                    direction = "CALL"  # ارتداد محتمل
                    base_strength = 0.4
            
            # تعديل القوة حسب الانضغاط
            strength_multiplier = 1.0
            squeeze_status = squeeze.get('status', 'طبيعي')
            if 'انضغاط' in squeeze_status:
                strength_multiplier += squeeze.get('strength', 0) * 0.5  # انضغاط يزيد احتمالية الحركة
            
            # تعديل القوة حسب الاتجاه
            trend_strength = trend.get('strength', 0)
            if trend.get('direction') == 'صاعد' and direction == "CALL":
                strength_multiplier += trend_strength * 0.3
            elif trend.get('direction') == 'هابط' and direction == "PUT":
                strength_multiplier += trend_strength * 0.3
            
            # حساب القوة النهائية
            final_strength = min(base_strength * strength_multiplier, 1.0)
            
            # حساب مستوى الثقة
            confidence = final_strength
            
            # زيادة الثقة مع الارتدادات
            if bounce.get('strength', 0) > 0.5:
                confidence *= 1.2
            
            # زيادة الثقة مع الانضغاط القوي
            if squeeze.get('strength', 0) > 0.7:
                confidence *= 1.1
            
            confidence = min(confidence, 1.0)
            
            # وزن Bollinger Bands متوسط إلى عالي
            weight = 1.1
            
            signal = {
                'direction': direction,
                'strength': final_strength,
                'confidence': confidence,
                'weight': weight,
                'details': {
                    'percent_b': percent_b,
                    'position': position,
                    'squeeze': squeeze,
                    'bounce': bounce,
                    'trend': trend,
                    'support_resistance': current_value.get('support_resistance', {})
                },
                'reasoning': self._get_signal_reasoning(direction, current_value),
                'timestamp': datetime.now()
            }
            
            return signal
            
        except Exception as e:
            logger.error(f"خطأ في تحليل إشارة Bollinger Bands: {str(e)}")
            return {
                'direction': 'NEUTRAL',
                'strength': 0.0,
                'confidence': 0.0,
                'weight': 1.0,
                'error': str(e)
            }
    
    def _get_signal_reasoning(self, direction: str, current_value: Dict[str, Any]) -> str:
        """
        الحصول على تفسير الإشارة
        
        Args:
            direction: اتجاه الإشارة
            current_value: القيمة الحالية
            
        Returns:
            تفسير نصي للإشارة
        """
        percent_b = current_value.get('percent_b', 50)
        zone = current_value.get('position_analysis', {}).get('zone', 'غير محدد')
        squeeze_status = current_value.get('squeeze_analysis', {}).get('status', 'طبيعي')
        bounce_type = current_value.get('bounce_analysis', {}).get('type', 'لا يوجد')
        
        if direction == "CALL":
            return f"BB: %B={percent_b:.1f} في {zone} مع {squeeze_status} و{bounce_type} - إشارة شراء"
        elif direction == "PUT":
            return f"BB: %B={percent_b:.1f} في {zone} مع {squeeze_status} و{bounce_type} - إشارة بيع"
        else:
            return f"BB: %B={percent_b:.1f} في {zone} مع {squeeze_status} - إشارة محايدة"
    
    def get_minimum_periods(self) -> int:
        """الحد الأدنى من الفترات المطلوبة"""
        return max(self.period * 2, 40)  # ضعف الفترة على الأقل
