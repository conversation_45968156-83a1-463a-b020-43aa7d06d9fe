"""
🔧 الكلاس الأساسي للمؤشرات الفنية
يحتوي على الوظائف الأساسية التي ترث منها جميع المؤشرات الفنية
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Union, Any
import pandas as pd
import numpy as np
from datetime import datetime
import logging

from config.constants import TechnicalIndicators, INDICATOR_SETTINGS

logger = logging.getLogger(__name__)


class BaseIndicator(ABC):
    """
    الكلاس الأساسي لجميع المؤشرات الفنية
    يوفر الوظائف الأساسية والواجهة الموحدة لجميع المؤشرات
    """
    
    def __init__(self, indicator_type: TechnicalIndicators, **kwargs):
        """
        تهيئة المؤشر الأساسي
        
        Args:
            indicator_type: نوع المؤشر من TechnicalIndicators
            **kwargs: معاملات إضافية خاصة بكل مؤشر
        """
        self.indicator_type = indicator_type
        self.name = indicator_type.value
        self.settings = INDICATOR_SETTINGS.get(self.name, {})
        self.settings.update(kwargs)
        
        # تحديث الإعدادات من المعاملات المرسلة
        for key, value in kwargs.items():
            if key in self.settings:
                self.settings[key] = value
        
        # متغيرات التخزين المؤقت
        self._cache = {}
        self._last_calculation_time = None
        self._is_initialized = False
        
        logger.info(f"تم تهيئة المؤشر: {self.name} بالإعدادات: {self.settings}")
    
    @abstractmethod
    def calculate(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        حساب قيم المؤشر
        
        Args:
            data: بيانات الشموع (DataFrame مع أعمدة OHLCV)
            
        Returns:
            Dict يحتوي على قيم المؤشر المحسوبة
        """
        pass
    
    @abstractmethod
    def get_signal(self, current_value: Dict[str, Any], previous_values: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        تحليل الإشارة من قيم المؤشر
        
        Args:
            current_value: القيمة الحالية للمؤشر
            previous_values: القيم السابقة للمؤشر
            
        Returns:
            Dict يحتوي على تحليل الإشارة
        """
        pass
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        """
        التحقق من صحة البيانات المدخلة
        
        Args:
            data: بيانات الشموع
            
        Returns:
            True إذا كانت البيانات صحيحة، False خلاف ذلك
        """
        try:
            # التحقق من وجود الأعمدة المطلوبة
            required_columns = ['open', 'high', 'low', 'close']
            if not all(col in data.columns for col in required_columns):
                logger.error(f"البيانات لا تحتوي على الأعمدة المطلوبة: {required_columns}")
                return False
            
            # التحقق من وجود بيانات كافية
            min_periods = self.get_minimum_periods()
            if len(data) < min_periods:
                logger.warning(f"البيانات غير كافية للمؤشر {self.name}. مطلوب: {min_periods}, متوفر: {len(data)}")
                return False
            
            # التحقق من عدم وجود قيم فارغة
            if data[required_columns].isnull().any().any():
                logger.warning(f"البيانات تحتوي على قيم فارغة للمؤشر {self.name}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من البيانات للمؤشر {self.name}: {str(e)}")
            return False
    
    def get_minimum_periods(self) -> int:
        """
        الحصول على الحد الأدنى من الفترات المطلوبة لحساب المؤشر
        
        Returns:
            عدد الفترات المطلوبة
        """
        # القيمة الافتراضية، يمكن تخصيصها في كل مؤشر
        return self.settings.get('period', 14)
    
    def format_output(self, raw_values: Dict[str, Any]) -> Dict[str, Any]:
        """
        تنسيق مخرجات المؤشر
        
        Args:
            raw_values: القيم الخام المحسوبة
            
        Returns:
            القيم منسقة ومعدة للاستخدام
        """
        formatted = {
            'indicator_name': self.name,
            'timestamp': datetime.now(),
            'values': raw_values,
            'settings': self.settings.copy()
        }
        
        return formatted
    
    def update_cache(self, key: str, value: Any) -> None:
        """
        تحديث التخزين المؤقت
        
        Args:
            key: مفتاح التخزين
            value: القيمة المراد تخزينها
        """
        self._cache[key] = value
        self._last_calculation_time = datetime.now()
    
    def get_from_cache(self, key: str) -> Optional[Any]:
        """
        الحصول على قيمة من التخزين المؤقت
        
        Args:
            key: مفتاح التخزين
            
        Returns:
            القيمة المخزنة أو None
        """
        return self._cache.get(key)
    
    def clear_cache(self) -> None:
        """مسح التخزين المؤقت"""
        self._cache.clear()
        self._last_calculation_time = None
    
    def get_info(self) -> Dict[str, Any]:
        """
        الحصول على معلومات المؤشر
        
        Returns:
            معلومات شاملة عن المؤشر
        """
        return {
            'name': self.name,
            'type': self.indicator_type.value,
            'settings': self.settings,
            'minimum_periods': self.get_minimum_periods(),
            'is_initialized': self._is_initialized,
            'last_calculation': self._last_calculation_time,
            'cache_size': len(self._cache)
        }
    
    def __str__(self) -> str:
        """تمثيل نصي للمؤشر"""
        return f"{self.name}({self.settings})"
    
    def __repr__(self) -> str:
        """تمثيل تقني للمؤشر"""
        return f"BaseIndicator(name='{self.name}', settings={self.settings})"


class IndicatorResult:
    """
    كلاس لتخزين نتائج المؤشرات بشكل منظم
    """
    
    def __init__(self, indicator_name: str, values: Dict[str, Any], signal: Dict[str, Any], timestamp: datetime = None):
        """
        تهيئة نتيجة المؤشر
        
        Args:
            indicator_name: اسم المؤشر
            values: قيم المؤشر
            signal: إشارة المؤشر
            timestamp: وقت الحساب
        """
        self.indicator_name = indicator_name
        self.values = values
        self.signal = signal
        self.timestamp = timestamp or datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل النتيجة إلى قاموس"""
        return {
            'indicator_name': self.indicator_name,
            'values': self.values,
            'signal': self.signal,
            'timestamp': self.timestamp.isoformat()
        }
    
    def __str__(self) -> str:
        return f"IndicatorResult({self.indicator_name}: {self.signal.get('direction', 'NEUTRAL')})"
