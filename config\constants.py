"""
ثوابت النظام
جميع الثوابت والقيم الثابتة المستخدمة في النظام
"""

from enum import Enum
from typing import Dict, List

# ==========================================
# 📊 ثوابت المؤشرات الفنية
# ==========================================

class TechnicalIndicators(Enum):
    """المؤشرات الفنية المدعومة"""
    EMA5 = "EMA5"
    EMA10 = "EMA10"
    EMA21 = "EMA21"
    SMA10 = "SMA10"
    RSI5 = "RSI5"
    RSI14 = "RSI14"
    MACD = "MACD"
    MOMENTUM10 = "MOMENTUM10"
    BOLLINGER_BANDS = "BOLLINGER_BANDS"
    ATR5 = "ATR5"
    ATR14 = "ATR14"
    HEIKEN_ASHI = "HEIKEN_ASHI"
    Z_SCORE = "Z_SCORE"

# إعدادات المؤشرات
INDICATOR_SETTINGS = {
    TechnicalIndicators.EMA5.value: {"period": 5},
    TechnicalIndicators.EMA10.value: {"period": 10},
    TechnicalIndicators.EMA21.value: {"period": 21},
    TechnicalIndicators.SMA10.value: {"period": 10},
    TechnicalIndicators.RSI5.value: {"period": 5},
    TechnicalIndicators.RSI14.value: {"period": 14},
    TechnicalIndicators.MACD.value: {"fast": 12, "slow": 26, "signal": 9},
    TechnicalIndicators.MOMENTUM10.value: {"period": 10},
    TechnicalIndicators.BOLLINGER_BANDS.value: {"period": 20, "std": 2},
    TechnicalIndicators.ATR5.value: {"period": 5},
    TechnicalIndicators.ATR14.value: {"period": 14},
    TechnicalIndicators.HEIKEN_ASHI.value: {},
    TechnicalIndicators.Z_SCORE.value: {"period": 20}
}

# ==========================================
# 💱 ثوابت أزواج العملات
# ==========================================

# الأزواج الرئيسية والثانوية
MAJOR_PAIRS = [
    "GBPUSD", "GBPUSD_otc", "USDJPY", "USDJPY_otc", "CHFJPY", "CHFJPY_otc",
    "USDCAD", "USDCAD_otc", "AUDCAD", "AUDCAD_otc", "USDCHF", "USDCHF_otc",
    "EURGBP", "EURGBP_otc", "EURAUD", "EURCAD", "AUDUSD", "AUDUSD_otc",
    "CADCHF", "CADCHF_otc", "EURJPY", "EURJPY_otc", "AUDCHF", "GBPCHF",
    "AUDJPY", "AUDJPY_otc", "GBPJPY", "GBPJPY_otc", "GBPAUD", "GBPAUD_otc",
    "GBPCAD", "CADJPY", "CADJPY_otc", "EURCHF", "EURCHF_otc", "EURUSD", "EURUSD_otc"
]

# الأزواج الغريبة والناشئة
EXOTIC_PAIRS = [
    "USDPHP_otc", "USDSGD_otc", "USDVND_otc", "USDMYR_otc", "NGNUSD_otc",
    "USDRUB_otc", "TNDUSD_otc", "NZDJPY_otc", "USDTHB_otc", "LBPUSD_otc",
    "USDBRL_otc", "USDPKR_otc", "EURNZD_otc", "USDDZD_otc", "USDEGP_otc",
    "NZDUSD_otc", "AUDNZD_otc", "YERUSD_otc", "EURHUF_otc", "USDMXN_otc",
    "IRRUSD_otc", "USDBDT_otc", "EURTRY_otc", "USDIDR_otc", "USDINR_otc",
    "USDCLP_otc", "USDCNH_otc", "USDCOP_otc", "ZARUSD_otc", "USDARS_otc",
    "EURRUB_otc", "CHFNOK_otc", "USDTRY_otc"
]

# جميع الأزواج (70 زوج)
ALL_CURRENCY_PAIRS = MAJOR_PAIRS + EXOTIC_PAIRS

# ==========================================
# 🕐 ثوابت الوقت
# ==========================================

class TimeFrames(Enum):
    """الأطر الزمنية المدعومة"""
    M1 = 60      # دقيقة واحدة
    M5 = 300     # 5 دقائق
    M15 = 900    # 15 دقيقة
    M30 = 1800   # 30 دقيقة
    H1 = 3600    # ساعة واحدة

# الإطار الزمني الافتراضي (5 دقائق)
DEFAULT_TIMEFRAME = TimeFrames.M5.value

# ==========================================
# 🔄 ثوابت النظام
# ==========================================

class SystemStatus(Enum):
    """حالات النظام"""
    STARTING = "starting"
    RUNNING = "running"
    PAUSED = "paused"
    STOPPING = "stopping"
    STOPPED = "stopped"
    ERROR = "error"

class AccountType(Enum):
    """أنواع الحسابات"""
    DEMO = "demo"
    REAL = "real"

class TradeDirection(Enum):
    """اتجاهات التداول"""
    CALL = "call"  # شراء
    PUT = "put"    # بيع

# ==========================================
# 📊 ثوابت قواعد البيانات
# ==========================================

# أسماء الجداول
TABLE_NAMES = {
    'currency_pairs': 'currency_pairs',
    'candles': 'candles',
    'technical_indicators': 'technical_indicators',
    'trades': 'trades',
    'risk_settings': 'risk_settings',
    'system_logs': 'system_logs'
}

# مفاتيح Redis
REDIS_KEYS = {
    'live_candles': 'live_candles:{pair}',
    'live_indicators': 'live_indicators:{pair}',
    'account_balance': 'account_balance:{account_type}',
    'payouts': 'payouts',
    'system_status': 'system_status',
    'active_trades': 'active_trades'
}

# ==========================================
# 🛡️ ثوابت إدارة المخاطر
# ==========================================

# حدود المخاطر الافتراضية
DEFAULT_RISK_SETTINGS = {
    'max_daily_loss_percent': 5.0,
    'max_concurrent_trades': 3,
    'min_balance_threshold': 10.0,
    'risk_per_trade_percent': 2.0,
    'max_trade_amount': 100.0,
    'min_trade_amount': 1.0
}

# ==========================================
# 📝 ثوابت السجلات
# ==========================================

class LogLevels(Enum):
    """مستويات السجلات"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

# تنسيق السجلات
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
DATE_FORMAT = "%Y-%m-%d %H:%M:%S"

# ==========================================
# 🌐 ثوابت الشبكة والاتصال
# ==========================================

# إعدادات الاتصال الافتراضية
DEFAULT_CONNECTION_SETTINGS = {
    'timeout': 60,
    'reconnect_time': 10,
    'max_retries': 3,
    'websocket_timeout': 30
}

# URLs الأساسية
POCKET_OPTION_URLS = {
    'websocket': 'wss://ws.pocketoption.com/socket.io/',
    'api_base': 'https://pocketoption.com/api/',
    'demo_url': 'https://pocketoption.com/cabinet/demo-quick-high-low/',
    'real_url': 'https://pocketoption.com/cabinet/quick-high-low/'
}

# ==========================================
# 🎯 ثوابت التداول
# ==========================================

# أنواع الخيارات
OPTION_TYPES = {
    'turbo': 'turbo',
    'binary': 'binary'
}

# مدد انتهاء الصلاحية (بالثواني)
EXPIRATION_TIMES = {
    'turbo_1m': 60,
    'turbo_2m': 120,
    'turbo_5m': 300,
    'binary_5m': 300,
    'binary_15m': 900,
    'binary_30m': 1800
}

# الحد الأدنى والأقصى لمبلغ التداول
TRADE_AMOUNT_LIMITS = {
    'min_amount': 1.0,
    'max_amount': 1000.0,
    'default_amount': 10.0
}

# ==========================================
# 🔧 ثوابت التحليل
# ==========================================

# عتبات المؤشرات
INDICATOR_THRESHOLDS = {
    'rsi_oversold': 30,
    'rsi_overbought': 70,
    'rsi_neutral_low': 40,
    'rsi_neutral_high': 60,
    'z_score_extreme': 2.0,
    'z_score_moderate': 1.5
}

# أوزان المؤشرات في اتخاذ القرار
INDICATOR_WEIGHTS = {
    TechnicalIndicators.EMA21.value: 0.15,
    TechnicalIndicators.RSI14.value: 0.12,
    TechnicalIndicators.MACD.value: 0.10,
    TechnicalIndicators.BOLLINGER_BANDS.value: 0.08,
    TechnicalIndicators.ATR14.value: 0.07,
    # باقي المؤشرات...
}

# ==========================================
# 📱 ثوابت الواجهة
# ==========================================

# ألوان الواجهة
UI_COLORS = {
    'success': '#28a745',
    'danger': '#dc3545',
    'warning': '#ffc107',
    'info': '#17a2b8',
    'primary': '#007bff',
    'secondary': '#6c757d'
}

# أحجام الرسوم البيانية
CHART_SIZES = {
    'small': (800, 400),
    'medium': (1200, 600),
    'large': (1600, 800)
}
