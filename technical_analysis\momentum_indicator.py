"""
📊 مؤشر الزخم (Momentum)
يحسب Momentum(10) مع تحليل قوة الحركة السعرية والإشارات
"""

from typing import Dict, List, Any
import pandas as pd
import numpy as np
from datetime import datetime
import logging

from .base_indicator import BaseIndicator
from config.constants import TechnicalIndicators

logger = logging.getLogger(__name__)


class MomentumIndicator(BaseIndicator):
    """
    مؤشر الزخم (Momentum)
    يقيس معدل التغيير في الأسعار ويحلل قوة الحركة السعرية
    """
    
    def __init__(self, period: int = 10, **kwargs):
        """
        تهيئة مؤشر Momentum
        
        Args:
            period: فترة حساب الزخم (افتراضي 10)
            **kwargs: معاملات إضافية
        """
        super().__init__(TechnicalIndicators.MOMENTUM10, period=period, **kwargs)
        self.period = period
        
        logger.info(f"تم تهيئة مؤشر Momentum{period}")
    
    def calculate(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        حساب قيم Momentum
        
        Args:
            data: بيانات الشموع مع أعمدة OHLC
            
        Returns:
            قاموس يحتوي على قيم Momentum المحسوبة
        """
        try:
            if not self.validate_data(data):
                raise ValueError("البيانات غير صحيحة")
            
            close_prices = data['close'].astype(float)
            high_prices = data['high'].astype(float)
            low_prices = data['low'].astype(float)
            
            # حساب Momentum الأساسي (السعر الحالي - السعر قبل n فترات)
            momentum = close_prices - close_prices.shift(self.period)
            
            # حساب Momentum النسبي (نسبة مئوية)
            momentum_percentage = ((close_prices / close_prices.shift(self.period)) - 1) * 100
            
            # حساب Momentum للأسعار الأخرى
            momentum_high = high_prices - high_prices.shift(self.period)
            momentum_low = low_prices - low_prices.shift(self.period)
            
            # حساب متوسط Momentum
            momentum_avg = momentum.rolling(window=5).mean()
            
            # القيم الحالية والسابقة
            current_momentum = float(momentum.iloc[-1]) if not pd.isna(momentum.iloc[-1]) else 0.0
            previous_momentum = float(momentum.iloc[-2]) if len(momentum) > 1 and not pd.isna(momentum.iloc[-2]) else current_momentum
            
            current_momentum_pct = float(momentum_percentage.iloc[-1]) if not pd.isna(momentum_percentage.iloc[-1]) else 0.0
            previous_momentum_pct = float(momentum_percentage.iloc[-2]) if len(momentum_percentage) > 1 and not pd.isna(momentum_percentage.iloc[-2]) else current_momentum_pct
            
            # حساب التغيير في الزخم
            momentum_change = current_momentum - previous_momentum
            momentum_acceleration = momentum_change
            
            # تحليل الاتجاه
            trend_analysis = self._analyze_momentum_trend(momentum, momentum_percentage)
            
            # تحليل القوة
            strength_analysis = self._analyze_momentum_strength(current_momentum, current_momentum_pct, momentum_change)
            
            # كشف التباعد
            divergence = self._detect_momentum_divergence(close_prices, momentum)
            
            # تحليل الإشارات
            signal_analysis = self._analyze_momentum_signals(current_momentum, previous_momentum, current_momentum_pct)
            
            # حساب التقلبات في الزخم
            momentum_volatility = float(momentum.tail(self.period).std()) if len(momentum) >= self.period else 0.0
            
            result = {
                'momentum_value': current_momentum,
                'momentum_percentage': current_momentum_pct,
                'previous_momentum': previous_momentum,
                'previous_momentum_pct': previous_momentum_pct,
                'momentum_change': momentum_change,
                'momentum_acceleration': momentum_acceleration,
                'momentum_avg': float(momentum_avg.iloc[-1]) if not pd.isna(momentum_avg.iloc[-1]) else 0.0,
                'momentum_high': float(momentum_high.iloc[-1]) if not pd.isna(momentum_high.iloc[-1]) else 0.0,
                'momentum_low': float(momentum_low.iloc[-1]) if not pd.isna(momentum_low.iloc[-1]) else 0.0,
                'momentum_volatility': momentum_volatility,
                'trend_analysis': trend_analysis,
                'strength_analysis': strength_analysis,
                'divergence': divergence,
                'signal_analysis': signal_analysis,
                'current_price': float(close_prices.iloc[-1]),
                'price_n_periods_ago': float(close_prices.iloc[-1-self.period]) if len(close_prices) > self.period else float(close_prices.iloc[0]),
                'period': self.period,
                'calculation_time': datetime.now()
            }
            
            # تحديث التخزين المؤقت
            self.update_cache('last_calculation', result)
            
            return result
            
        except Exception as e:
            logger.error(f"خطأ في حساب Momentum{self.period}: {str(e)}")
            raise
    
    def _analyze_momentum_trend(self, momentum: pd.Series, momentum_pct: pd.Series) -> Dict[str, Any]:
        """
        تحليل اتجاه الزخم
        
        Args:
            momentum: قيم الزخم المطلقة
            momentum_pct: قيم الزخم النسبية
            
        Returns:
            تحليل الاتجاه
        """
        try:
            if len(momentum) < 5:
                return {'direction': 'غير محدد', 'strength': 0.0, 'consistency': 0.0}
            
            recent_momentum = momentum.tail(5).dropna()
            if len(recent_momentum) < 3:
                return {'direction': 'غير محدد', 'strength': 0.0, 'consistency': 0.0}
            
            # حساب الاتجاه العام
            trend_slope = (recent_momentum.iloc[-1] - recent_momentum.iloc[0]) / len(recent_momentum)
            
            # تحديد الاتجاه
            if trend_slope > 0.01:
                direction = 'صاعد'
                strength = min(abs(trend_slope) * 100, 1.0)
            elif trend_slope < -0.01:
                direction = 'هابط'
                strength = min(abs(trend_slope) * 100, 1.0)
            else:
                direction = 'جانبي'
                strength = 0.0
            
            # حساب الاتساق (كم من القيم تتبع نفس الاتجاه)
            positive_count = (recent_momentum > 0).sum()
            negative_count = (recent_momentum < 0).sum()
            consistency = max(positive_count, negative_count) / len(recent_momentum)
            
            return {
                'direction': direction,
                'strength': strength,
                'consistency': consistency,
                'slope': trend_slope
            }
            
        except Exception:
            return {'direction': 'خطأ', 'strength': 0.0, 'consistency': 0.0, 'slope': 0.0}
    
    def _analyze_momentum_strength(self, current_momentum: float, current_momentum_pct: float, momentum_change: float) -> Dict[str, Any]:
        """
        تحليل قوة الزخم
        
        Args:
            current_momentum: الزخم الحالي
            current_momentum_pct: الزخم النسبي الحالي
            momentum_change: التغيير في الزخم
            
        Returns:
            تحليل القوة
        """
        try:
            # تصنيف قوة الزخم
            abs_momentum_pct = abs(current_momentum_pct)
            
            if abs_momentum_pct > 5.0:
                strength_level = 'قوي جداً'
                strength_score = 1.0
            elif abs_momentum_pct > 3.0:
                strength_level = 'قوي'
                strength_score = 0.8
            elif abs_momentum_pct > 1.5:
                strength_level = 'متوسط'
                strength_score = 0.6
            elif abs_momentum_pct > 0.5:
                strength_level = 'ضعيف'
                strength_score = 0.4
            else:
                strength_level = 'ضعيف جداً'
                strength_score = 0.2
            
            # تحليل التسارع
            if abs(momentum_change) > abs(current_momentum) * 0.5:
                acceleration = 'متسارع'
                acceleration_score = 1.0
            elif abs(momentum_change) > abs(current_momentum) * 0.2:
                acceleration = 'متوسط'
                acceleration_score = 0.6
            else:
                acceleration = 'بطيء'
                acceleration_score = 0.3
            
            return {
                'level': strength_level,
                'score': strength_score,
                'acceleration': acceleration,
                'acceleration_score': acceleration_score,
                'momentum_pct': current_momentum_pct
            }
            
        except Exception:
            return {
                'level': 'خطأ',
                'score': 0.0,
                'acceleration': 'خطأ',
                'acceleration_score': 0.0,
                'momentum_pct': 0.0
            }
    
    def _detect_momentum_divergence(self, prices: pd.Series, momentum: pd.Series) -> Dict[str, Any]:
        """
        كشف التباعد بين السعر والزخم
        
        Args:
            prices: أسعار الإغلاق
            momentum: قيم الزخم
            
        Returns:
            معلومات التباعد
        """
        try:
            if len(prices) < 10 or len(momentum) < 10:
                return {'type': 'لا يوجد', 'strength': 0.0}
            
            # أخذ آخر 8 نقاط للتحليل
            recent_prices = prices.tail(8)
            recent_momentum = momentum.tail(8).dropna()
            
            if len(recent_momentum) < 6:
                return {'type': 'لا يوجد', 'strength': 0.0}
            
            # حساب الاتجاه العام
            price_trend = recent_prices.iloc[-1] - recent_prices.iloc[0]
            momentum_trend = recent_momentum.iloc[-1] - recent_momentum.iloc[0]
            
            # كشف التباعد
            if price_trend > 0 and momentum_trend < 0:
                divergence_type = 'تباعد هبوطي'
                strength = min(abs(momentum_trend) / abs(price_trend) * 10, 1.0)
            elif price_trend < 0 and momentum_trend > 0:
                divergence_type = 'تباعد صعودي'
                strength = min(abs(momentum_trend) / abs(price_trend) * 10, 1.0)
            else:
                divergence_type = 'لا يوجد'
                strength = 0.0
            
            return {
                'type': divergence_type,
                'strength': strength,
                'price_trend': price_trend,
                'momentum_trend': momentum_trend
            }
            
        except Exception:
            return {'type': 'خطأ في الحساب', 'strength': 0.0}
    
    def _analyze_momentum_signals(self, current_momentum: float, previous_momentum: float, momentum_pct: float) -> Dict[str, Any]:
        """
        تحليل إشارات الزخم
        
        Args:
            current_momentum: الزخم الحالي
            previous_momentum: الزخم السابق
            momentum_pct: الزخم النسبي
            
        Returns:
            تحليل الإشارات
        """
        try:
            signals = []
            signal_strength = 0.0
            
            # إشارة تغيير الاتجاه
            if previous_momentum < 0 and current_momentum > 0:
                signals.append('تحول إلى إيجابي')
                signal_strength += 0.4
            elif previous_momentum > 0 and current_momentum < 0:
                signals.append('تحول إلى سلبي')
                signal_strength += 0.4
            
            # إشارة قوة الزخم
            if abs(momentum_pct) > 3.0:
                signals.append('زخم قوي')
                signal_strength += 0.3
            elif abs(momentum_pct) > 1.5:
                signals.append('زخم متوسط')
                signal_strength += 0.2
            
            # إشارة التسارع
            momentum_change = current_momentum - previous_momentum
            if abs(momentum_change) > abs(current_momentum) * 0.3:
                signals.append('تسارع في الزخم')
                signal_strength += 0.3
            
            return {
                'signals': signals,
                'strength': min(signal_strength, 1.0),
                'count': len(signals)
            }
            
        except Exception:
            return {'signals': [], 'strength': 0.0, 'count': 0}
    
    def get_signal(self, current_value: Dict[str, Any], previous_values: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        تحليل إشارة Momentum
        
        Args:
            current_value: القيمة الحالية لـ Momentum
            previous_values: القيم السابقة (للتحليل المتقدم)
            
        Returns:
            تحليل الإشارة
        """
        try:
            # استخراج البيانات
            momentum_value = current_value.get('momentum_value', 0)
            momentum_pct = current_value.get('momentum_percentage', 0)
            momentum_change = current_value.get('momentum_change', 0)
            trend_analysis = current_value.get('trend_analysis', {})
            strength_analysis = current_value.get('strength_analysis', {})
            divergence = current_value.get('divergence', {})
            signal_analysis = current_value.get('signal_analysis', {})
            
            # تحليل الإشارة الأساسية
            direction = "NEUTRAL"
            base_strength = 0.1
            
            # إشارات الزخم الإيجابي/السلبي
            if momentum_value > 0 and momentum_change > 0:
                direction = "CALL"  # زخم إيجابي متزايد
                base_strength = 0.6
            elif momentum_value < 0 and momentum_change < 0:
                direction = "PUT"  # زخم سلبي متزايد
                base_strength = 0.6
            # إشارات تغيير الاتجاه
            elif momentum_value > 0 and momentum_change < 0:
                direction = "NEUTRAL"  # زخم إيجابي لكن يتراجع
                base_strength = 0.3
            elif momentum_value < 0 and momentum_change > 0:
                direction = "NEUTRAL"  # زخم سلبي لكن يتحسن
                base_strength = 0.3
            
            # تعديل القوة حسب قوة الزخم
            strength_multiplier = 1.0
            strength_score = strength_analysis.get('score', 0)
            strength_multiplier += strength_score * 0.5
            
            # تعديل القوة حسب الاتساق
            consistency = trend_analysis.get('consistency', 0)
            strength_multiplier += consistency * 0.3
            
            # تعديل القوة حسب التباعد
            divergence_type = divergence.get('type', 'لا يوجد')
            if divergence_type == 'تباعد صعودي' and direction == "CALL":
                strength_multiplier += divergence.get('strength', 0) * 0.4
            elif divergence_type == 'تباعد هبوطي' and direction == "PUT":
                strength_multiplier += divergence.get('strength', 0) * 0.4
            
            # حساب القوة النهائية
            final_strength = min(base_strength * strength_multiplier, 1.0)
            
            # حساب مستوى الثقة
            confidence = final_strength
            
            # زيادة الثقة مع الزخم القوي
            if abs(momentum_pct) > 3.0:
                confidence *= 1.2
            
            # زيادة الثقة مع الاتساق العالي
            if consistency > 0.8:
                confidence *= 1.1
            
            confidence = min(confidence, 1.0)
            
            # وزن Momentum متوسط
            weight = 0.9
            
            signal = {
                'direction': direction,
                'strength': final_strength,
                'confidence': confidence,
                'weight': weight,
                'details': {
                    'momentum_value': momentum_value,
                    'momentum_percentage': momentum_pct,
                    'momentum_change': momentum_change,
                    'trend': trend_analysis,
                    'strength_level': strength_analysis,
                    'divergence': divergence,
                    'signals': signal_analysis
                },
                'reasoning': self._get_signal_reasoning(direction, current_value),
                'timestamp': datetime.now()
            }
            
            return signal
            
        except Exception as e:
            logger.error(f"خطأ في تحليل إشارة Momentum{self.period}: {str(e)}")
            return {
                'direction': 'NEUTRAL',
                'strength': 0.0,
                'confidence': 0.0,
                'weight': 1.0,
                'error': str(e)
            }
    
    def _get_signal_reasoning(self, direction: str, current_value: Dict[str, Any]) -> str:
        """
        الحصول على تفسير الإشارة
        
        Args:
            direction: اتجاه الإشارة
            current_value: القيمة الحالية
            
        Returns:
            تفسير نصي للإشارة
        """
        momentum_pct = current_value.get('momentum_percentage', 0)
        trend = current_value.get('trend_analysis', {}).get('direction', 'غير محدد')
        strength_level = current_value.get('strength_analysis', {}).get('level', 'غير محدد')
        
        if direction == "CALL":
            return f"Momentum{self.period}: {momentum_pct:.2f}% {trend} بقوة {strength_level} - إشارة شراء"
        elif direction == "PUT":
            return f"Momentum{self.period}: {momentum_pct:.2f}% {trend} بقوة {strength_level} - إشارة بيع"
        else:
            return f"Momentum{self.period}: {momentum_pct:.2f}% {trend} بقوة {strength_level} - إشارة محايدة"
    
    def get_minimum_periods(self) -> int:
        """الحد الأدنى من الفترات المطلوبة"""
        return max(self.period * 2, 25)  # ضعف الفترة على الأقل


# كلاس محدد لـ Momentum10
class Momentum10(MomentumIndicator):
    """مؤشر Momentum لفترة 10"""
    def __init__(self, **kwargs):
        super().__init__(period=10, **kwargs)
