"""
🔧 وحدة التحليل الفني
تحتوي على جميع المؤشرات الفنية والأدوات المطلوبة للتحليل الفني المتقدم
"""

from .base_indicator import BaseIndicator, IndicatorResult
from .indicators_engine import IndicatorsEngine

# استيراد جميع المؤشرات الفنية
from .ema_indicators import EMAIndicator, EMA5, EMA10, EMA21
from .sma_indicator import SMAIndicator, SMA10
from .rsi_indicators import RSIIndicator, RSI5, RSI14
from .macd_indicator import MACDIndicator
from .momentum_indicator import MomentumIndicator, Momentum10
from .bollinger_bands_indicator import BollingerBandsIndicator
from .atr_indicators import ATRIndicator, ATR5, ATR14
from .heiken_ashi_indicator import HeikenAshiIndicator
from .z_score_indicator import ZScoreIndicator

__all__ = [
    # الكلاسات الأساسية
    'BaseIndicator',
    'IndicatorResult',
    'IndicatorsEngine',

    # المؤشرات العامة
    'EMAIndicator',
    'SMAIndicator',
    'RSIIndicator',
    'MACDIndicator',
    'MomentumIndicator',
    'BollingerBandsIndicator',
    'ATRIndicator',
    'HeikenAshiIndicator',
    'ZScoreIndicator',

    # المؤشرات المحددة
    'EMA5', 'EMA10', 'EMA21',
    'SMA10',
    'RSI5', 'RSI14',
    'Momentum10',
    'ATR5', 'ATR14'
]

__version__ = "2.0.0"
__author__ = "Scalping Trading System"
__description__ = "نظام التحليل الفني المتقدم للسكالبينغ - 15 مؤشر فني"
