"""
📈 مؤشرات المتوسط المتحرك الأسي (EMA)
تحتوي على EMA5, EMA10, EMA21 مع تحليل الإشارات
"""

from typing import Dict, List, Any
import pandas as pd
import numpy as np
from datetime import datetime
import logging

from .base_indicator import BaseIndicator
from config.constants import TechnicalIndicators

logger = logging.getLogger(__name__)


class EMAIndicator(BaseIndicator):
    """
    مؤشر المتوسط المتحرك الأسي (Exponential Moving Average)
    يحسب EMA للفترات المختلفة ويحلل الإشارات
    """
    
    def __init__(self, period: int = 14, **kwargs):
        """
        تهيئة مؤشر EMA
        
        Args:
            period: فترة المتوسط المتحرك
            **kwargs: معاملات إضافية
        """
        # تحديد نوع المؤشر حسب الفترة
        if period == 5:
            indicator_type = TechnicalIndicators.EMA5
        elif period == 10:
            indicator_type = TechnicalIndicators.EMA10
        elif period == 21:
            indicator_type = TechnicalIndicators.EMA21
        else:
            # للفترات المخصصة
            indicator_type = TechnicalIndicators.EMA21
        
        super().__init__(indicator_type, period=period, **kwargs)
        self.period = period
        
        logger.info(f"تم تهيئة مؤشر EMA{period}")
    
    def calculate(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        حساب قيم EMA
        
        Args:
            data: بيانات الشموع مع أعمدة OHLC
            
        Returns:
            قاموس يحتوي على قيم EMA المحسوبة
        """
        try:
            if not self.validate_data(data):
                raise ValueError("البيانات غير صحيحة")
            
            # حساب EMA للأسعار المختلفة
            close_prices = data['close'].astype(float)
            
            # حساب EMA الأساسي (للإغلاق)
            ema_close = close_prices.ewm(span=self.period, adjust=False).mean()
            
            # حساب EMA للأسعار الأخرى للتحليل المتقدم
            ema_high = data['high'].astype(float).ewm(span=self.period, adjust=False).mean()
            ema_low = data['low'].astype(float).ewm(span=self.period, adjust=False).mean()
            ema_open = data['open'].astype(float).ewm(span=self.period, adjust=False).mean()
            
            # القيم الحالية والسابقة
            current_ema = float(ema_close.iloc[-1])
            previous_ema = float(ema_close.iloc[-2]) if len(ema_close) > 1 else current_ema
            
            # حساب الميل والاتجاه
            slope = current_ema - previous_ema
            slope_percentage = (slope / previous_ema) * 100 if previous_ema != 0 else 0
            
            # حساب المسافة من السعر الحالي
            current_price = float(close_prices.iloc[-1])
            distance_from_price = current_price - current_ema
            distance_percentage = (distance_from_price / current_ema) * 100 if current_ema != 0 else 0
            
            # تحديد الاتجاه
            if slope > 0:
                trend = "صاعد"
                trend_strength = min(abs(slope_percentage) * 10, 1.0)
            elif slope < 0:
                trend = "هابط"
                trend_strength = min(abs(slope_percentage) * 10, 1.0)
            else:
                trend = "جانبي"
                trend_strength = 0.0
            
            result = {
                'ema_value': current_ema,
                'previous_ema': previous_ema,
                'slope': slope,
                'slope_percentage': slope_percentage,
                'trend': trend,
                'trend_strength': trend_strength,
                'distance_from_price': distance_from_price,
                'distance_percentage': distance_percentage,
                'current_price': current_price,
                'ema_high': float(ema_high.iloc[-1]),
                'ema_low': float(ema_low.iloc[-1]),
                'ema_open': float(ema_open.iloc[-1]),
                'period': self.period,
                'calculation_time': datetime.now()
            }
            
            # تحديث التخزين المؤقت
            self.update_cache('last_calculation', result)
            
            return result
            
        except Exception as e:
            logger.error(f"خطأ في حساب EMA{self.period}: {str(e)}")
            raise
    
    def get_signal(self, current_value: Dict[str, Any], previous_values: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        تحليل إشارة EMA
        
        Args:
            current_value: القيمة الحالية لـ EMA
            previous_values: القيم السابقة (للتحليل المتقدم)
            
        Returns:
            تحليل الإشارة
        """
        try:
            # استخراج البيانات
            current_price = current_value.get('current_price', 0)
            ema_value = current_value.get('ema_value', 0)
            slope = current_value.get('slope', 0)
            trend_strength = current_value.get('trend_strength', 0)
            distance_percentage = current_value.get('distance_percentage', 0)
            
            # تحليل الإشارة الأساسية
            if current_price > ema_value and slope > 0:
                direction = "CALL"
                base_strength = 0.7
            elif current_price < ema_value and slope < 0:
                direction = "PUT"
                base_strength = 0.7
            elif current_price > ema_value and slope < 0:
                direction = "NEUTRAL"  # تضارب في الإشارات
                base_strength = 0.3
            elif current_price < ema_value and slope > 0:
                direction = "NEUTRAL"  # تضارب في الإشارات
                base_strength = 0.3
            else:
                direction = "NEUTRAL"
                base_strength = 0.1
            
            # تعديل القوة حسب المسافة من EMA
            distance_factor = min(abs(distance_percentage) / 2.0, 1.0)  # كلما زادت المسافة زادت القوة
            
            # تعديل القوة حسب قوة الاتجاه
            trend_factor = trend_strength
            
            # حساب القوة النهائية
            final_strength = min(base_strength * (1 + distance_factor) * (1 + trend_factor), 1.0)
            
            # تحديد مستوى الثقة
            confidence = final_strength
            if abs(distance_percentage) > 1.0:  # إذا كان السعر بعيد عن EMA
                confidence *= 1.2
            if trend_strength > 0.5:  # إذا كان الاتجاه قوي
                confidence *= 1.1
            
            confidence = min(confidence, 1.0)
            
            # تحديد الوزن حسب فترة EMA
            if self.period == 5:
                weight = 0.8  # EMA قصير المدى - وزن أقل
            elif self.period == 10:
                weight = 1.0  # EMA متوسط المدى - وزن متوسط
            elif self.period == 21:
                weight = 1.2  # EMA طويل المدى - وزن أكبر
            else:
                weight = 1.0
            
            signal = {
                'direction': direction,
                'strength': final_strength,
                'confidence': confidence,
                'weight': weight,
                'details': {
                    'price_vs_ema': 'أعلى' if current_price > ema_value else 'أقل',
                    'trend': current_value.get('trend', 'غير محدد'),
                    'slope': slope,
                    'distance_percentage': distance_percentage,
                    'trend_strength': trend_strength
                },
                'reasoning': self._get_signal_reasoning(direction, current_value),
                'timestamp': datetime.now()
            }
            
            return signal
            
        except Exception as e:
            logger.error(f"خطأ في تحليل إشارة EMA{self.period}: {str(e)}")
            return {
                'direction': 'NEUTRAL',
                'strength': 0.0,
                'confidence': 0.0,
                'weight': 1.0,
                'error': str(e)
            }
    
    def _get_signal_reasoning(self, direction: str, current_value: Dict[str, Any]) -> str:
        """
        الحصول على تفسير الإشارة
        
        Args:
            direction: اتجاه الإشارة
            current_value: القيمة الحالية
            
        Returns:
            تفسير نصي للإشارة
        """
        price_vs_ema = 'أعلى' if current_value.get('current_price', 0) > current_value.get('ema_value', 0) else 'أقل'
        trend = current_value.get('trend', 'غير محدد')
        
        if direction == "CALL":
            return f"السعر {price_vs_ema} من EMA{self.period} والاتجاه {trend} - إشارة شراء"
        elif direction == "PUT":
            return f"السعر {price_vs_ema} من EMA{self.period} والاتجاه {trend} - إشارة بيع"
        else:
            return f"إشارة محايدة - السعر {price_vs_ema} من EMA{self.period} والاتجاه {trend}"
    
    def get_minimum_periods(self) -> int:
        """الحد الأدنى من الفترات المطلوبة"""
        return max(self.period * 2, 20)  # ضعف الفترة على الأقل


# كلاسات محددة لكل فترة EMA
class EMA5(EMAIndicator):
    """مؤشر EMA لفترة 5"""
    def __init__(self, **kwargs):
        super().__init__(period=5, **kwargs)


class EMA10(EMAIndicator):
    """مؤشر EMA لفترة 10"""
    def __init__(self, **kwargs):
        super().__init__(period=10, **kwargs)


class EMA21(EMAIndicator):
    """مؤشر EMA لفترة 21"""
    def __init__(self, **kwargs):
        super().__init__(period=21, **kwargs)
