"""
📊 مؤشر شموع هايكن آشي (Heiken Ash<PERSON>)
يحول الشموع العادية إلى شموع هايكن آشي لتحليل الاتجاه بوضوح أكبر
"""

from typing import Dict, List, Any
import pandas as pd
import numpy as np
from datetime import datetime
import logging

from .base_indicator import BaseIndicator
from config.constants import TechnicalIndicators

logger = logging.getLogger(__name__)


class HeikenAshiIndicator(BaseIndicator):
    """
    مؤشر شموع هايكن آشي (Heiken Ashi)
    يحول الشموع التقليدية إلى شموع هايكن آشي لتحليل الاتجاه بشكل أوضح
    """
    
    def __init__(self, **kwargs):
        """
        تهيئة مؤشر Heiken Ashi
        
        Args:
            **kwargs: معاملات إضافية
        """
        super().__init__(TechnicalIndicators.HEIKEN_ASHI, **kwargs)
        
        logger.info("تم تهيئة مؤشر Heiken Ashi")
    
    def calculate(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        حساب قيم Heiken Ashi
        
        Args:
            data: بيانات الشموع مع أعمدة OHLC
            
        Returns:
            قاموس يحتوي على قيم Heiken Ashi المحسوبة
        """
        try:
            if not self.validate_data(data):
                raise ValueError("البيانات غير صحيحة")
            
            # استخراج البيانات الأصلية
            open_prices = data['open'].astype(float)
            high_prices = data['high'].astype(float)
            low_prices = data['low'].astype(float)
            close_prices = data['close'].astype(float)
            
            # إنشاء DataFrame لشموع Heiken Ashi
            ha_data = pd.DataFrame(index=data.index)
            
            # حساب Heiken Ashi Close
            # HA_Close = (Open + High + Low + Close) / 4
            ha_data['ha_close'] = (open_prices + high_prices + low_prices + close_prices) / 4
            
            # حساب Heiken Ashi Open
            # HA_Open = (Previous HA_Open + Previous HA_Close) / 2
            ha_data['ha_open'] = np.nan
            ha_data.iloc[0, ha_data.columns.get_loc('ha_open')] = (open_prices.iloc[0] + close_prices.iloc[0]) / 2
            
            for i in range(1, len(ha_data)):
                ha_data.iloc[i, ha_data.columns.get_loc('ha_open')] = (
                    ha_data.iloc[i-1, ha_data.columns.get_loc('ha_open')] + 
                    ha_data.iloc[i-1, ha_data.columns.get_loc('ha_close')]
                ) / 2
            
            # حساب Heiken Ashi High و Low
            # HA_High = max(High, HA_Open, HA_Close)
            # HA_Low = min(Low, HA_Open, HA_Close)
            ha_data['ha_high'] = pd.concat([high_prices, ha_data['ha_open'], ha_data['ha_close']], axis=1).max(axis=1)
            ha_data['ha_low'] = pd.concat([low_prices, ha_data['ha_open'], ha_data['ha_close']], axis=1).min(axis=1)
            
            # القيم الحالية والسابقة
            current_ha = {
                'open': float(ha_data['ha_open'].iloc[-1]),
                'high': float(ha_data['ha_high'].iloc[-1]),
                'low': float(ha_data['ha_low'].iloc[-1]),
                'close': float(ha_data['ha_close'].iloc[-1])
            }
            
            previous_ha = {
                'open': float(ha_data['ha_open'].iloc[-2]) if len(ha_data) > 1 else current_ha['open'],
                'high': float(ha_data['ha_high'].iloc[-2]) if len(ha_data) > 1 else current_ha['high'],
                'low': float(ha_data['ha_low'].iloc[-2]) if len(ha_data) > 1 else current_ha['low'],
                'close': float(ha_data['ha_close'].iloc[-2]) if len(ha_data) > 1 else current_ha['close']
            }
            
            # تحليل نوع الشمعة
            candle_analysis = self._analyze_candle_type(current_ha, previous_ha)
            
            # تحليل الاتجاه
            trend_analysis = self._analyze_trend(ha_data)
            
            # تحليل قوة الحركة
            momentum_analysis = self._analyze_momentum(current_ha, previous_ha)
            
            # كشف أنماط الانعكاس
            reversal_patterns = self._detect_reversal_patterns(ha_data)
            
            # تحليل الاستمرارية
            continuation_analysis = self._analyze_continuation(ha_data)
            
            # حساب قوة الإشارة
            signal_strength = self._calculate_signal_strength(candle_analysis, trend_analysis, momentum_analysis)
            
            result = {
                'current_ha': current_ha,
                'previous_ha': previous_ha,
                'original_candle': {
                    'open': float(open_prices.iloc[-1]),
                    'high': float(high_prices.iloc[-1]),
                    'low': float(low_prices.iloc[-1]),
                    'close': float(close_prices.iloc[-1])
                },
                'candle_analysis': candle_analysis,
                'trend_analysis': trend_analysis,
                'momentum_analysis': momentum_analysis,
                'reversal_patterns': reversal_patterns,
                'continuation_analysis': continuation_analysis,
                'signal_strength': signal_strength,
                'ha_data_recent': ha_data.tail(5).to_dict('records'),  # آخر 5 شموع للتحليل
                'calculation_time': datetime.now()
            }
            
            # تحديث التخزين المؤقت
            self.update_cache('last_calculation', result)
            
            return result
            
        except Exception as e:
            logger.error(f"خطأ في حساب Heiken Ashi: {str(e)}")
            raise
    
    def _analyze_candle_type(self, current_ha: Dict[str, float], previous_ha: Dict[str, float]) -> Dict[str, Any]:
        """
        تحليل نوع شمعة Heiken Ashi
        
        Args:
            current_ha: الشمعة الحالية
            previous_ha: الشمعة السابقة
            
        Returns:
            تحليل نوع الشمعة
        """
        try:
            # تحديد لون الشمعة
            if current_ha['close'] > current_ha['open']:
                color = 'خضراء'  # صاعدة
                direction = 'صاعد'
            elif current_ha['close'] < current_ha['open']:
                color = 'حمراء'  # هابطة
                direction = 'هابط'
            else:
                color = 'محايدة'  # دوجي
                direction = 'محايد'
            
            # حساب حجم الجسم والظلال
            body_size = abs(current_ha['close'] - current_ha['open'])
            upper_shadow = current_ha['high'] - max(current_ha['open'], current_ha['close'])
            lower_shadow = min(current_ha['open'], current_ha['close']) - current_ha['low']
            total_range = current_ha['high'] - current_ha['low']
            
            # نسب الجسم والظلال
            body_ratio = (body_size / total_range) * 100 if total_range != 0 else 0
            upper_shadow_ratio = (upper_shadow / total_range) * 100 if total_range != 0 else 0
            lower_shadow_ratio = (lower_shadow / total_range) * 100 if total_range != 0 else 0
            
            # تصنيف قوة الشمعة
            if body_ratio > 70:
                strength = 'قوية جداً'
                strength_score = 1.0
            elif body_ratio > 50:
                strength = 'قوية'
                strength_score = 0.8
            elif body_ratio > 30:
                strength = 'متوسطة'
                strength_score = 0.6
            elif body_ratio > 10:
                strength = 'ضعيفة'
                strength_score = 0.4
            else:
                strength = 'ضعيفة جداً'
                strength_score = 0.2
            
            # تحليل الظلال
            if upper_shadow_ratio > 40:
                shadow_analysis = 'ظل علوي طويل - مقاومة'
            elif lower_shadow_ratio > 40:
                shadow_analysis = 'ظل سفلي طويل - دعم'
            elif upper_shadow_ratio < 10 and lower_shadow_ratio < 10:
                shadow_analysis = 'ظلال قصيرة - قوة اتجاه'
            else:
                shadow_analysis = 'ظلال متوازنة'
            
            return {
                'color': color,
                'direction': direction,
                'strength': strength,
                'strength_score': strength_score,
                'body_size': body_size,
                'body_ratio': body_ratio,
                'upper_shadow': upper_shadow,
                'lower_shadow': lower_shadow,
                'upper_shadow_ratio': upper_shadow_ratio,
                'lower_shadow_ratio': lower_shadow_ratio,
                'shadow_analysis': shadow_analysis,
                'total_range': total_range
            }
            
        except Exception:
            return {
                'color': 'خطأ',
                'direction': 'غير محدد',
                'strength': 'غير محدد',
                'strength_score': 0.0
            }
    
    def _analyze_trend(self, ha_data: pd.DataFrame) -> Dict[str, Any]:
        """
        تحليل الاتجاه من شموع Heiken Ashi
        
        Args:
            ha_data: بيانات شموع Heiken Ashi
            
        Returns:
            تحليل الاتجاه
        """
        try:
            if len(ha_data) < 5:
                return {'direction': 'غير محدد', 'strength': 0.0, 'consistency': 0.0}
            
            recent_candles = ha_data.tail(5)
            
            # عد الشموع الصاعدة والهابطة
            bullish_count = (recent_candles['ha_close'] > recent_candles['ha_open']).sum()
            bearish_count = (recent_candles['ha_close'] < recent_candles['ha_open']).sum()
            
            # تحديد الاتجاه
            if bullish_count >= 4:
                direction = 'صاعد قوي'
                strength = 0.9
            elif bullish_count >= 3:
                direction = 'صاعد'
                strength = 0.7
            elif bearish_count >= 4:
                direction = 'هابط قوي'
                strength = 0.9
            elif bearish_count >= 3:
                direction = 'هابط'
                strength = 0.7
            else:
                direction = 'جانبي'
                strength = 0.3
            
            # حساب الاتساق
            consistency = max(bullish_count, bearish_count) / len(recent_candles)
            
            # تحليل التغيير في الاتجاه
            current_color = 'صاعد' if recent_candles['ha_close'].iloc[-1] > recent_candles['ha_open'].iloc[-1] else 'هابط'
            previous_color = 'صاعد' if recent_candles['ha_close'].iloc[-2] > recent_candles['ha_open'].iloc[-2] else 'هابط'
            
            trend_change = current_color != previous_color
            
            return {
                'direction': direction,
                'strength': strength,
                'consistency': consistency,
                'bullish_count': bullish_count,
                'bearish_count': bearish_count,
                'trend_change': trend_change,
                'current_color': current_color,
                'previous_color': previous_color
            }
            
        except Exception:
            return {'direction': 'خطأ', 'strength': 0.0, 'consistency': 0.0}
    
    def _analyze_momentum(self, current_ha: Dict[str, float], previous_ha: Dict[str, float]) -> Dict[str, Any]:
        """
        تحليل الزخم من شموع Heiken Ashi
        
        Args:
            current_ha: الشمعة الحالية
            previous_ha: الشمعة السابقة
            
        Returns:
            تحليل الزخم
        """
        try:
            # حساب التغيير في الأسعار
            close_change = current_ha['close'] - previous_ha['close']
            open_change = current_ha['open'] - previous_ha['open']
            
            # حساب حجم الجسم للشمعتين
            current_body = abs(current_ha['close'] - current_ha['open'])
            previous_body = abs(previous_ha['close'] - previous_ha['open'])
            
            # تحليل تغيير حجم الجسم
            body_change = current_body - previous_body
            body_change_pct = (body_change / previous_body) * 100 if previous_body != 0 else 0
            
            # تحديد قوة الزخم
            if abs(close_change) > current_body * 0.5:
                momentum_strength = 'قوي'
                momentum_score = 0.8
            elif abs(close_change) > current_body * 0.2:
                momentum_strength = 'متوسط'
                momentum_score = 0.6
            else:
                momentum_strength = 'ضعيف'
                momentum_score = 0.4
            
            # تحديد اتجاه الزخم
            if close_change > 0:
                momentum_direction = 'صاعد'
            elif close_change < 0:
                momentum_direction = 'هابط'
            else:
                momentum_direction = 'محايد'
            
            return {
                'direction': momentum_direction,
                'strength': momentum_strength,
                'score': momentum_score,
                'close_change': close_change,
                'body_change': body_change,
                'body_change_percentage': body_change_pct,
                'current_body_size': current_body,
                'previous_body_size': previous_body
            }
            
        except Exception:
            return {
                'direction': 'خطأ',
                'strength': 'غير محدد',
                'score': 0.0
            }
    
    def _detect_reversal_patterns(self, ha_data: pd.DataFrame) -> Dict[str, Any]:
        """
        كشف أنماط الانعكاس في شموع Heiken Ashi
        
        Args:
            ha_data: بيانات شموع Heiken Ashi
            
        Returns:
            أنماط الانعكاس المكتشفة
        """
        try:
            if len(ha_data) < 3:
                return {'pattern': 'لا يوجد', 'strength': 0.0}
            
            recent_candles = ha_data.tail(3)
            
            # تحليل آخر 3 شموع
            colors = []
            for i in range(len(recent_candles)):
                if recent_candles['ha_close'].iloc[i] > recent_candles['ha_open'].iloc[i]:
                    colors.append('صاعد')
                else:
                    colors.append('هابط')
            
            # كشف أنماط الانعكاس
            if colors == ['هابط', 'هابط', 'صاعد']:
                pattern = 'انعكاس صعودي محتمل'
                strength = 0.7
            elif colors == ['صاعد', 'صاعد', 'هابط']:
                pattern = 'انعكاس هبوطي محتمل'
                strength = 0.7
            elif colors == ['هابط', 'صاعد', 'صاعد']:
                pattern = 'تأكيد اتجاه صعودي'
                strength = 0.6
            elif colors == ['صاعد', 'هابط', 'هابط']:
                pattern = 'تأكيد اتجاه هبوطي'
                strength = 0.6
            else:
                pattern = 'لا يوجد نمط واضح'
                strength = 0.0
            
            return {
                'pattern': pattern,
                'strength': strength,
                'colors_sequence': colors
            }
            
        except Exception:
            return {'pattern': 'خطأ', 'strength': 0.0}
    
    def _analyze_continuation(self, ha_data: pd.DataFrame) -> Dict[str, Any]:
        """
        تحليل استمرارية الاتجاه
        
        Args:
            ha_data: بيانات شموع Heiken Ashi
            
        Returns:
            تحليل الاستمرارية
        """
        try:
            if len(ha_data) < 5:
                return {'continuation': 'غير محدد', 'probability': 0.5}
            
            recent_candles = ha_data.tail(5)
            
            # عد الشموع المتتالية من نفس اللون
            consecutive_bullish = 0
            consecutive_bearish = 0
            
            for i in range(len(recent_candles)-1, -1, -1):
                if recent_candles['ha_close'].iloc[i] > recent_candles['ha_open'].iloc[i]:
                    if consecutive_bearish == 0:
                        consecutive_bullish += 1
                    else:
                        break
                else:
                    if consecutive_bullish == 0:
                        consecutive_bearish += 1
                    else:
                        break
            
            # تحديد احتمالية الاستمرار
            if consecutive_bullish >= 3:
                continuation = 'استمرار صعودي محتمل'
                probability = min(0.6 + (consecutive_bullish - 3) * 0.1, 0.9)
            elif consecutive_bearish >= 3:
                continuation = 'استمرار هبوطي محتمل'
                probability = min(0.6 + (consecutive_bearish - 3) * 0.1, 0.9)
            else:
                continuation = 'غير واضح'
                probability = 0.5
            
            return {
                'continuation': continuation,
                'probability': probability,
                'consecutive_bullish': consecutive_bullish,
                'consecutive_bearish': consecutive_bearish
            }
            
        except Exception:
            return {'continuation': 'خطأ', 'probability': 0.5}
    
    def _calculate_signal_strength(self, candle_analysis: Dict[str, Any], trend_analysis: Dict[str, Any], momentum_analysis: Dict[str, Any]) -> float:
        """
        حساب قوة الإشارة الإجمالية
        
        Args:
            candle_analysis, trend_analysis, momentum_analysis: التحليلات المختلفة
            
        Returns:
            قوة الإشارة (0-1)
        """
        strength = 0.0
        
        # قوة الشمعة
        strength += candle_analysis.get('strength_score', 0) * 0.3
        
        # قوة الاتجاه
        strength += trend_analysis.get('strength', 0) * 0.4
        
        # قوة الزخم
        strength += momentum_analysis.get('score', 0) * 0.3
        
        return min(strength, 1.0)
    
    def get_signal(self, current_value: Dict[str, Any], previous_values: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        تحليل إشارة Heiken Ashi
        
        Args:
            current_value: القيمة الحالية
            previous_values: القيم السابقة
            
        Returns:
            تحليل الإشارة
        """
        try:
            # استخراج البيانات
            candle_analysis = current_value.get('candle_analysis', {})
            trend_analysis = current_value.get('trend_analysis', {})
            momentum_analysis = current_value.get('momentum_analysis', {})
            reversal_patterns = current_value.get('reversal_patterns', {})
            continuation_analysis = current_value.get('continuation_analysis', {})
            
            # تحليل الإشارة الأساسية
            direction = "NEUTRAL"
            base_strength = 0.1
            
            # إشارات الاتجاه
            trend_direction = trend_analysis.get('direction', 'جانبي')
            if 'صاعد' in trend_direction:
                direction = "CALL"
                base_strength = 0.7 if 'قوي' in trend_direction else 0.5
            elif 'هابط' in trend_direction:
                direction = "PUT"
                base_strength = 0.7 if 'قوي' in trend_direction else 0.5
            
            # إشارات الانعكاس
            reversal_pattern = reversal_patterns.get('pattern', 'لا يوجد')
            if 'انعكاس صعودي' in reversal_pattern:
                direction = "CALL"
                base_strength = max(base_strength, 0.6)
            elif 'انعكاس هبوطي' in reversal_pattern:
                direction = "PUT"
                base_strength = max(base_strength, 0.6)
            
            # تعديل القوة حسب قوة الشمعة
            strength_multiplier = 1.0
            candle_strength = candle_analysis.get('strength_score', 0)
            strength_multiplier += candle_strength * 0.3
            
            # تعديل القوة حسب الاتساق
            consistency = trend_analysis.get('consistency', 0)
            strength_multiplier += consistency * 0.2
            
            # تعديل القوة حسب الزخم
            momentum_score = momentum_analysis.get('score', 0)
            if momentum_analysis.get('direction') == candle_analysis.get('direction'):
                strength_multiplier += momentum_score * 0.2
            
            # حساب القوة النهائية
            final_strength = min(base_strength * strength_multiplier, 1.0)
            
            # حساب مستوى الثقة
            confidence = final_strength
            
            # زيادة الثقة مع الاتجاهات القوية
            if 'قوي' in trend_direction:
                confidence *= 1.2
            
            # زيادة الثقة مع أنماط الانعكاس
            if reversal_patterns.get('strength', 0) > 0.6:
                confidence *= 1.1
            
            confidence = min(confidence, 1.0)
            
            # وزن Heiken Ashi عالي لتحليل الاتجاه
            weight = 1.2
            
            signal = {
                'direction': direction,
                'strength': final_strength,
                'confidence': confidence,
                'weight': weight,
                'details': {
                    'candle_color': candle_analysis.get('color', 'غير محدد'),
                    'candle_strength': candle_analysis.get('strength', 'غير محدد'),
                    'trend_direction': trend_direction,
                    'trend_consistency': consistency,
                    'momentum_direction': momentum_analysis.get('direction', 'غير محدد'),
                    'reversal_pattern': reversal_pattern,
                    'continuation': continuation_analysis.get('continuation', 'غير محدد'),
                    'ha_candle': current_value.get('current_ha', {})
                },
                'reasoning': self._get_signal_reasoning(direction, current_value),
                'timestamp': datetime.now()
            }
            
            return signal
            
        except Exception as e:
            logger.error(f"خطأ في تحليل إشارة Heiken Ashi: {str(e)}")
            return {
                'direction': 'NEUTRAL',
                'strength': 0.0,
                'confidence': 0.0,
                'weight': 1.0,
                'error': str(e)
            }
    
    def _get_signal_reasoning(self, direction: str, current_value: Dict[str, Any]) -> str:
        """
        الحصول على تفسير الإشارة
        
        Args:
            direction: اتجاه الإشارة
            current_value: القيمة الحالية
            
        Returns:
            تفسير نصي للإشارة
        """
        candle_color = current_value.get('candle_analysis', {}).get('color', 'غير محدد')
        trend_direction = current_value.get('trend_analysis', {}).get('direction', 'غير محدد')
        reversal_pattern = current_value.get('reversal_patterns', {}).get('pattern', 'لا يوجد')
        
        if direction == "CALL":
            return f"Heiken Ashi: شمعة {candle_color} مع اتجاه {trend_direction} و{reversal_pattern} - إشارة شراء"
        elif direction == "PUT":
            return f"Heiken Ashi: شمعة {candle_color} مع اتجاه {trend_direction} و{reversal_pattern} - إشارة بيع"
        else:
            return f"Heiken Ashi: شمعة {candle_color} مع اتجاه {trend_direction} - إشارة محايدة"
    
    def get_minimum_periods(self) -> int:
        """الحد الأدنى من الفترات المطلوبة"""
        return 10  # نحتاج عدد قليل من الشموع لحساب Heiken Ashi
