"""
📊 مؤشر MACD (تقارب وتباعد المتوسطات المتحركة)
يحسب MACD(12,26,9) مع تحليل الإشارات والتقاطعات
"""

from typing import Dict, List, Any
import pandas as pd
import numpy as np
from datetime import datetime
import logging

from .base_indicator import BaseIndicator
from config.constants import TechnicalIndicators

logger = logging.getLogger(__name__)


class MACDIndicator(BaseIndicator):
    """
    مؤشر MACD (Moving Average Convergence Divergence)
    يحسب MACD ويحلل التقاطعات والإشارات
    """
    
    def __init__(self, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9, **kwargs):
        """
        تهيئة مؤشر MACD
        
        Args:
            fast_period: فترة EMA السريع (افتراضي 12)
            slow_period: فترة EMA البطيء (افتراضي 26)
            signal_period: فترة خط الإشارة (افتراضي 9)
            **kwargs: معاملات إضافية
        """
        super().__init__(TechnicalIndicators.MACD, 
                        fast=fast_period, 
                        slow=slow_period, 
                        signal=signal_period, 
                        **kwargs)
        
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.signal_period = signal_period
        
        logger.info(f"تم تهيئة مؤشر MACD({fast_period},{slow_period},{signal_period})")
    
    def calculate(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        حساب قيم MACD
        
        Args:
            data: بيانات الشموع مع أعمدة OHLC
            
        Returns:
            قاموس يحتوي على قيم MACD المحسوبة
        """
        try:
            if not self.validate_data(data):
                raise ValueError("البيانات غير صحيحة")
            
            close_prices = data['close'].astype(float)
            
            # حساب EMA السريع والبطيء
            ema_fast = close_prices.ewm(span=self.fast_period, adjust=False).mean()
            ema_slow = close_prices.ewm(span=self.slow_period, adjust=False).mean()
            
            # حساب خط MACD
            macd_line = ema_fast - ema_slow
            
            # حساب خط الإشارة (EMA لخط MACD)
            signal_line = macd_line.ewm(span=self.signal_period, adjust=False).mean()
            
            # حساب الهيستوجرام
            histogram = macd_line - signal_line
            
            # القيم الحالية والسابقة
            current_macd = float(macd_line.iloc[-1])
            current_signal = float(signal_line.iloc[-1])
            current_histogram = float(histogram.iloc[-1])
            
            previous_macd = float(macd_line.iloc[-2]) if len(macd_line) > 1 else current_macd
            previous_signal = float(signal_line.iloc[-2]) if len(signal_line) > 1 else current_signal
            previous_histogram = float(histogram.iloc[-2]) if len(histogram) > 1 else current_histogram
            
            # تحليل التقاطعات
            crossover_analysis = self._analyze_crossovers(macd_line, signal_line, histogram)
            
            # تحليل الاتجاه
            macd_trend = self._analyze_trend(macd_line)
            signal_trend = self._analyze_trend(signal_line)
            histogram_trend = self._analyze_trend(histogram)
            
            # حساب قوة الإشارة
            signal_strength = self._calculate_signal_strength(
                current_macd, current_signal, current_histogram,
                previous_macd, previous_signal, previous_histogram,
                crossover_analysis
            )
            
            # تحليل التباعد
            divergence = self._detect_divergence(close_prices, macd_line)
            
            result = {
                'macd_line': current_macd,
                'signal_line': current_signal,
                'histogram': current_histogram,
                'previous_macd': previous_macd,
                'previous_signal': previous_signal,
                'previous_histogram': previous_histogram,
                'ema_fast': float(ema_fast.iloc[-1]),
                'ema_slow': float(ema_slow.iloc[-1]),
                'macd_change': current_macd - previous_macd,
                'signal_change': current_signal - previous_signal,
                'histogram_change': current_histogram - previous_histogram,
                'crossover_analysis': crossover_analysis,
                'macd_trend': macd_trend,
                'signal_trend': signal_trend,
                'histogram_trend': histogram_trend,
                'signal_strength': signal_strength,
                'divergence': divergence,
                'periods': {
                    'fast': self.fast_period,
                    'slow': self.slow_period,
                    'signal': self.signal_period
                },
                'calculation_time': datetime.now()
            }
            
            # تحديث التخزين المؤقت
            self.update_cache('last_calculation', result)
            
            return result
            
        except Exception as e:
            logger.error(f"خطأ في حساب MACD: {str(e)}")
            raise
    
    def _analyze_crossovers(self, macd_line: pd.Series, signal_line: pd.Series, histogram: pd.Series) -> Dict[str, Any]:
        """
        تحليل التقاطعات
        
        Args:
            macd_line: خط MACD
            signal_line: خط الإشارة
            histogram: الهيستوجرام
            
        Returns:
            تحليل التقاطعات
        """
        try:
            if len(histogram) < 3:
                return {'type': 'غير كافي', 'strength': 0.0, 'confirmed': False}
            
            current_hist = histogram.iloc[-1]
            previous_hist = histogram.iloc[-2]
            before_previous_hist = histogram.iloc[-3]
            
            # كشف التقاطع الصعودي
            if previous_hist <= 0 and current_hist > 0:
                crossover_type = 'تقاطع صعودي'
                strength = min(abs(current_hist) * 100, 1.0)
                confirmed = before_previous_hist < previous_hist  # تأكيد الاتجاه
            # كشف التقاطع الهبوطي
            elif previous_hist >= 0 and current_hist < 0:
                crossover_type = 'تقاطع هبوطي'
                strength = min(abs(current_hist) * 100, 1.0)
                confirmed = before_previous_hist > previous_hist  # تأكيد الاتجاه
            # لا يوجد تقاطع
            else:
                crossover_type = 'لا يوجد'
                strength = 0.0
                confirmed = False
            
            return {
                'type': crossover_type,
                'strength': strength,
                'confirmed': confirmed,
                'histogram_values': [before_previous_hist, previous_hist, current_hist]
            }
            
        except Exception:
            return {'type': 'خطأ', 'strength': 0.0, 'confirmed': False}
    
    def _analyze_trend(self, series: pd.Series) -> Dict[str, Any]:
        """
        تحليل اتجاه السلسلة
        
        Args:
            series: السلسلة المراد تحليلها
            
        Returns:
            تحليل الاتجاه
        """
        try:
            if len(series) < 5:
                return {'direction': 'غير محدد', 'strength': 0.0}
            
            recent_values = series.tail(5)
            slope = (recent_values.iloc[-1] - recent_values.iloc[0]) / 4
            
            if slope > 0.001:
                direction = 'صاعد'
                strength = min(abs(slope) * 1000, 1.0)
            elif slope < -0.001:
                direction = 'هابط'
                strength = min(abs(slope) * 1000, 1.0)
            else:
                direction = 'جانبي'
                strength = 0.0
            
            return {'direction': direction, 'strength': strength, 'slope': slope}
            
        except Exception:
            return {'direction': 'خطأ', 'strength': 0.0, 'slope': 0.0}
    
    def _calculate_signal_strength(self, current_macd: float, current_signal: float, current_histogram: float,
                                 previous_macd: float, previous_signal: float, previous_histogram: float,
                                 crossover_analysis: Dict[str, Any]) -> float:
        """
        حساب قوة الإشارة
        
        Args:
            current_macd, current_signal, current_histogram: القيم الحالية
            previous_macd, previous_signal, previous_histogram: القيم السابقة
            crossover_analysis: تحليل التقاطعات
            
        Returns:
            قوة الإشارة (0-1)
        """
        strength = 0.0
        
        # قوة التقاطع
        if crossover_analysis.get('type') != 'لا يوجد':
            strength += crossover_analysis.get('strength', 0) * 0.4
            if crossover_analysis.get('confirmed', False):
                strength += 0.2
        
        # قوة الهيستوجرام
        histogram_strength = min(abs(current_histogram) * 50, 0.3)
        strength += histogram_strength
        
        # اتجاه MACD
        macd_momentum = abs(current_macd - previous_macd)
        strength += min(macd_momentum * 100, 0.1)
        
        return min(strength, 1.0)
    
    def _detect_divergence(self, prices: pd.Series, macd: pd.Series) -> Dict[str, Any]:
        """
        كشف التباعد بين السعر و MACD
        
        Args:
            prices: أسعار الإغلاق
            macd: قيم MACD
            
        Returns:
            معلومات التباعد
        """
        try:
            if len(prices) < 10 or len(macd) < 10:
                return {'type': 'لا يوجد', 'strength': 0.0}
            
            # أخذ آخر 10 نقاط للتحليل
            recent_prices = prices.tail(10)
            recent_macd = macd.tail(10)
            
            # حساب الاتجاه العام
            price_trend = recent_prices.iloc[-1] - recent_prices.iloc[0]
            macd_trend = recent_macd.iloc[-1] - recent_macd.iloc[0]
            
            # كشف التباعد
            if price_trend > 0 and macd_trend < 0:
                return {'type': 'تباعد هبوطي', 'strength': min(abs(macd_trend) * 100, 1.0)}
            elif price_trend < 0 and macd_trend > 0:
                return {'type': 'تباعد صعودي', 'strength': min(abs(macd_trend) * 100, 1.0)}
            else:
                return {'type': 'لا يوجد', 'strength': 0.0}
                
        except Exception:
            return {'type': 'خطأ في الحساب', 'strength': 0.0}
    
    def get_signal(self, current_value: Dict[str, Any], previous_values: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        تحليل إشارة MACD
        
        Args:
            current_value: القيمة الحالية لـ MACD
            previous_values: القيم السابقة (للتحليل المتقدم)
            
        Returns:
            تحليل الإشارة
        """
        try:
            # استخراج البيانات
            macd_line = current_value.get('macd_line', 0)
            signal_line = current_value.get('signal_line', 0)
            histogram = current_value.get('histogram', 0)
            crossover_analysis = current_value.get('crossover_analysis', {})
            macd_trend = current_value.get('macd_trend', {})
            divergence = current_value.get('divergence', {})
            signal_strength = current_value.get('signal_strength', 0)
            
            # تحليل الإشارة الأساسية
            direction = "NEUTRAL"
            base_strength = 0.1
            
            # إشارات التقاطع
            crossover_type = crossover_analysis.get('type', 'لا يوجد')
            if crossover_type == 'تقاطع صعودي':
                direction = "CALL"
                base_strength = 0.8 if crossover_analysis.get('confirmed', False) else 0.6
            elif crossover_type == 'تقاطع هبوطي':
                direction = "PUT"
                base_strength = 0.8 if crossover_analysis.get('confirmed', False) else 0.6
            
            # إشارات الموقع
            elif macd_line > signal_line and histogram > 0:
                if macd_trend.get('direction') == 'صاعد':
                    direction = "CALL"
                    base_strength = 0.5
            elif macd_line < signal_line and histogram < 0:
                if macd_trend.get('direction') == 'هابط':
                    direction = "PUT"
                    base_strength = 0.5
            
            # تعديل القوة حسب التباعد
            strength_multiplier = 1.0
            divergence_type = divergence.get('type', 'لا يوجد')
            if divergence_type == 'تباعد صعودي' and direction == "CALL":
                strength_multiplier += divergence.get('strength', 0) * 0.5
            elif divergence_type == 'تباعد هبوطي' and direction == "PUT":
                strength_multiplier += divergence.get('strength', 0) * 0.5
            
            # تعديل القوة حسب قوة الاتجاه
            trend_strength = macd_trend.get('strength', 0)
            strength_multiplier += trend_strength * 0.3
            
            # حساب القوة النهائية
            final_strength = min(base_strength * strength_multiplier, 1.0)
            
            # حساب مستوى الثقة
            confidence = final_strength
            
            # زيادة الثقة مع التقاطعات المؤكدة
            if crossover_analysis.get('confirmed', False):
                confidence *= 1.2
            
            # زيادة الثقة مع التباعد
            if divergence.get('strength', 0) > 0.5:
                confidence *= 1.1
            
            confidence = min(confidence, 1.0)
            
            # وزن MACD متوسط
            weight = 1.0
            
            signal = {
                'direction': direction,
                'strength': final_strength,
                'confidence': confidence,
                'weight': weight,
                'details': {
                    'macd_line': macd_line,
                    'signal_line': signal_line,
                    'histogram': histogram,
                    'crossover': crossover_analysis,
                    'trend': macd_trend,
                    'divergence': divergence
                },
                'reasoning': self._get_signal_reasoning(direction, current_value),
                'timestamp': datetime.now()
            }
            
            return signal
            
        except Exception as e:
            logger.error(f"خطأ في تحليل إشارة MACD: {str(e)}")
            return {
                'direction': 'NEUTRAL',
                'strength': 0.0,
                'confidence': 0.0,
                'weight': 1.0,
                'error': str(e)
            }
    
    def _get_signal_reasoning(self, direction: str, current_value: Dict[str, Any]) -> str:
        """
        الحصول على تفسير الإشارة
        
        Args:
            direction: اتجاه الإشارة
            current_value: القيمة الحالية
            
        Returns:
            تفسير نصي للإشارة
        """
        crossover = current_value.get('crossover_analysis', {}).get('type', 'لا يوجد')
        divergence = current_value.get('divergence', {}).get('type', 'لا يوجد')
        histogram = current_value.get('histogram', 0)
        
        if direction == "CALL":
            return f"MACD: {crossover} مع هيستوجرام {histogram:.4f} و{divergence} - إشارة شراء"
        elif direction == "PUT":
            return f"MACD: {crossover} مع هيستوجرام {histogram:.4f} و{divergence} - إشارة بيع"
        else:
            return f"MACD: {crossover} مع هيستوجرام {histogram:.4f} - إشارة محايدة"
    
    def get_minimum_periods(self) -> int:
        """الحد الأدنى من الفترات المطلوبة"""
        return max(self.slow_period + self.signal_period, 50)  # فترة كافية لحساب MACD
